/* pages/statistics/index.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 标签栏样式 */
.tabs {
  display: flex;
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.tab {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab.active {
  color: #3B82F6;
  font-weight: 500;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 6rpx;
  background-color: #3B82F6;
  border-radius: 3rpx;
}

/* 加载中样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
}

.loading {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #3B82F6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 统计卡片样式 */
.stats-card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.stats-header {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  padding-bottom: 15rpx;
}

.stats-grid {
  display: flex;
  justify-content: space-between;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stats-value {
  font-size: 40rpx;
  font-weight: 600;
  color: #3B82F6;
  margin-bottom: 10rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
}

/* 每日记录样式 */
.daily-list {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.daily-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.daily-item:last-child {
  border-bottom: none;
}

.daily-date {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 15rpx;
}

.daily-details {
  display: flex;
  flex-wrap: wrap;
}

.daily-detail {
  width: 50%;
  margin-bottom: 10rpx;
  font-size: 24rpx;
}

.detail-label {
  color: #666;
  margin-right: 10rpx;
}

.detail-value {
  color: #3B82F6;
  font-weight: 500;
}

/* 活动列表样式 */
.activity-list {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.activity-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.activity-type {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}

.activity-detail {
  font-size: 24rpx;
  margin-bottom: 5rpx;
}

.load-more {
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #3B82F6;
}

.empty-tip {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 28rpx;
}

/* 深色模式 */
.tab-content.dark-mode {
  background: #2c3e50;
  color: #ecf0f1;
}

.dark-mode .trend-controls,
.dark-mode .trend-chart,
.dark-mode .trend-summary,
.dark-mode .activity-distribution-chart,
.dark-mode .time-slot-analysis {
  background-color: #34495e;
  color: #ecf0f1;
}

.dark-mode .summary-label,
.dark-mode .chart-title {
  color: #bdc3c7;
}

.dark-mode .summary-value {
  color: #74b9ff;
}

/* 趋势页面样式 */
.trend-controls {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15rpx;
}

.trend-selector {
  display: flex;
  align-items: center;
}

.trend-selector text {
  font-size: 28rpx;
  color: #666;
  margin-right: 10rpx;
}

.picker {
  font-size: 28rpx;
  color: #3B82F6;
  font-weight: 500;
  padding: 5rpx 20rpx;
  background-color: #f0f7ff;
  border-radius: 8rpx;
}

.trend-chart {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.trend-data {
  display: flex;
  align-items: flex-end;
  height: 240rpx;
  padding: 20rpx 0;
  overflow-x: auto;
}

.trend-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 20rpx;
  min-width: 60rpx;
}

.trend-date {
  font-size: 20rpx;
  color: #999;
  margin-bottom: 10rpx;
  transform: rotate(-45deg);
  white-space: nowrap;
  margin-top: 20rpx;
}

.trend-bar {
  width: 30rpx;
  background: linear-gradient(to top, #3B82F6, #60a5fa);
  border-radius: 4rpx;
  transition: height 0.3s;
}

.trend-value {
  font-size: 20rpx;
  color: #666;
  margin-top: 10rpx;
}

.trend-summary {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.trend-summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15rpx;
  font-size: 28rpx;
}

.summary-label {
  color: #666;
}

.summary-value {
  color: #3B82F6;
  font-weight: 500;
}

/* 图表类型选择器 */
.chart-type-selector {
  display: flex;
  gap: 10rpx;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 8rpx;
  padding: 6rpx;
}

.chart-type-btn {
  padding: 12rpx 16rpx;
  border-radius: 6rpx;
  transition: all 0.3s ease;
  cursor: pointer;
}

.chart-type-btn.active {
  background: #3B82F6;
  color: white;
  transform: scale(1.05);
}

.chart-icon {
  font-size: 24rpx;
}

/* 数据标签开关 */
.data-labels-toggle {
  display: flex;
  align-items: center;
  gap: 12rpx;
  cursor: pointer;
}

.toggle-label {
  font-size: 26rpx;
  color: #666;
}

.switch {
  width: 60rpx;
  height: 32rpx;
  background: #bdc3c7;
  border-radius: 16rpx;
  position: relative;
  transition: all 0.3s ease;
}

.switch.active {
  background: #3B82F6;
}

.switch-thumb {
  width: 24rpx;
  height: 24rpx;
  background: white;
  border-radius: 50%;
  position: absolute;
  top: 4rpx;
  left: 4rpx;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.switch.active .switch-thumb {
  transform: translateX(28rpx);
}

/* 图表头部 */
.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #ecf0f1;
}

.dark-mode .chart-header {
  border-bottom-color: rgba(255, 255, 255, 0.2);
}

.chart-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #2c3e50;
}

.chart-legend {
  display: flex;
  gap: 20rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.legend-color {
  width: 16rpx;
  height: 16rpx;
  border-radius: 2rpx;
}

.legend-text {
  font-size: 24rpx;
  color: #666;
}

/* 增强的趋势图表 */
.trend-data {
  position: relative;
}

.trend-value-top {
  font-size: 20rpx;
  color: #666;
  margin-bottom: 8rpx;
  text-align: center;
}

.chart-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.grid-line {
  position: absolute;
  left: 0;
  right: 0;
  height: 1rpx;
  background: rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
}

.dark-mode .grid-line {
  background: rgba(255, 255, 255, 0.2);
}

.grid-label {
  font-size: 20rpx;
  color: #999;
  background: white;
  padding: 0 8rpx;
  margin-left: -40rpx;
}

.dark-mode .grid-label {
  background: #34495e;
  color: #bdc3c7;
}

/* 学习活动分布图表 */
.activity-distribution-chart,
.time-slot-analysis {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.distribution-data {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.distribution-item {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.activity-label {
  font-size: 26rpx;
  color: #2c3e50;
  min-width: 120rpx;
  font-weight: 500;
}

.dark-mode .activity-label {
  color: #ecf0f1;
}

.activity-bar-container {
  flex: 1;
  position: relative;
  height: 32rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  overflow: hidden;
}

.dark-mode .activity-bar-container {
  background: rgba(255, 255, 255, 0.1);
}

.activity-bar {
  height: 100%;
  border-radius: 16rpx;
  transition: width 0.5s ease;
  position: relative;
}

.activity-percentage {
  position: absolute;
  right: 10rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 22rpx;
  color: white;
  font-weight: 500;
}

.activity-count {
  font-size: 24rpx;
  color: #666;
  min-width: 80rpx;
  text-align: right;
}

.dark-mode .activity-count {
  color: #bdc3c7;
}

/* 学习时间段分析 */
.time-slot-data {
  display: flex;
  align-items: flex-end;
  height: 200rpx;
  gap: 8rpx;
  overflow-x: auto;
  padding: 20rpx 0;
}

.time-slot-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 60rpx;
}

.time-label {
  font-size: 20rpx;
  color: #999;
  margin-bottom: 10rpx;
  transform: rotate(-45deg);
  white-space: nowrap;
}

.time-bar {
  width: 24rpx;
  min-height: 8rpx;
  border-radius: 4rpx;
  transition: height 0.5s ease;
  margin-bottom: 10rpx;
}

.time-value {
  font-size: 20rpx;
  color: #666;
  text-align: center;
}

.dark-mode .time-value {
  color: #bdc3c7;
}

/* 图表操作按钮 */
.chart-actions {
  display: flex;
  gap: 15rpx;
  margin-top: 30rpx;
  flex-wrap: wrap;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 20rpx;
  border: none;
  border-radius: 12rpx;
  font-size: 26rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  min-width: 140rpx;
}

.action-btn:active {
  transform: scale(0.98);
}

.export-btn {
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.share-btn {
  background: linear-gradient(45deg, #3498db 0%, #2980b9 100%);
  color: white;
}

.timeline-btn {
  background: linear-gradient(45deg, #2ecc71 0%, #27ae60 100%);
  color: white;
}

.btn-icon {
  font-size: 28rpx;
}

.btn-text {
  font-weight: 500;
}

<!--pages/statistics/index.wxml-->
<view class="container">
  <!-- 顶部标签栏 -->
  <view class="tabs">
    <view class="tab {{currentTab === 'overview' ? 'active' : ''}}" bindtap="switchTab" data-tab="overview">总览</view>
    <view class="tab {{currentTab === 'daily' ? 'active' : ''}}" bindtap="switchTab" data-tab="daily">每日记录</view>
    <view class="tab {{currentTab === 'activities' ? 'active' : ''}}" bindtap="switchTab" data-tab="activities">学习活动</view>
    <view class="tab {{currentTab === 'trend' ? 'active' : ''}}" bindtap="switchTab" data-tab="trend">学习趋势</view>
  </view>

  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading"></view>
    <text>加载中...</text>
  </view>

  <!-- 总览标签页 -->
  <view class="tab-content" wx:if="{{currentTab === 'overview' && !isLoading}}">
    <view class="stats-card">
      <view class="stats-header">学习概况</view>
      <view class="stats-grid">
        <view class="stats-item">
          <text class="stats-value">{{statistics.totalStudyDays || 0}}</text>
          <text class="stats-label">总学习天数</text>
        </view>
        <view class="stats-item">
          <text class="stats-value">{{statistics.currentStreak || 0}}</text>
          <text class="stats-label">当前连续天数</text>
        </view>
        <view class="stats-item">
          <text class="stats-value">{{statistics.longestStreak || 0}}</text>
          <text class="stats-label">最长连续天数</text>
        </view>
      </view>
    </view>

    <view class="stats-card">
      <view class="stats-header">学习内容</view>
      <view class="stats-grid">
        <view class="stats-item">
          <text class="stats-value">{{statistics.completedExercises || 0}}</text>
          <text class="stats-label">完成练习</text>
        </view>
        <view class="stats-item">
          <text class="stats-value">{{statistics.viewedInsights || 0}}</text>
          <text class="stats-label">查看观点</text>
        </view>
        <view class="stats-item">
          <text class="stats-value">{{statistics.createdNotes || 0}}</text>
          <text class="stats-label">创建笔记</text>
        </view>
      </view>
    </view>

    <view class="stats-card">
      <view class="stats-header">学习计划</view>
      <view class="stats-grid">
        <view class="stats-item">
          <text class="stats-value">{{statistics.activePlans || 0}}</text>
          <text class="stats-label">活跃计划</text>
        </view>
        <view class="stats-item">
          <text class="stats-value">{{statistics.completedPlans || 0}}</text>
          <text class="stats-label">完成计划</text>
        </view>
        <view class="stats-item">
          <text class="stats-value">{{statistics.totalTimeSpent || 0}}</text>
          <text class="stats-label">总学习时间(分钟)</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 每日记录标签页 -->
  <view class="tab-content" wx:if="{{currentTab === 'daily' && !isLoading}}">
    <view class="daily-list">
      <view class="daily-item" wx:for="{{dailyRecords}}" wx:key="date">
        <view class="daily-date">{{item.date}}</view>
        <view class="daily-details">
          <view class="daily-detail">
            <text class="detail-label">学习时间:</text>
            <text class="detail-value">{{item.timeSpent}}分钟</text>
          </view>
          <view class="daily-detail">
            <text class="detail-label">完成练习:</text>
            <text class="detail-value">{{item.exercisesCompleted}}个</text>
          </view>
          <view class="daily-detail">
            <text class="detail-label">查看观点:</text>
            <text class="detail-value">{{item.insightsViewed}}个</text>
          </view>
          <view class="daily-detail">
            <text class="detail-label">创建笔记:</text>
            <text class="detail-value">{{item.notesCreated}}个</text>
          </view>
          <view class="daily-detail">
            <text class="detail-label">泡泡互动:</text>
            <text class="detail-value">{{item.bubbleInteractions}}次</text>
          </view>
        </view>
      </view>
      <view class="empty-tip" wx:if="{{dailyRecords.length === 0}}">
        暂无每日学习记录
      </view>
    </view>
  </view>

  <!-- 学习活动标签页 -->
  <view class="tab-content" wx:if="{{currentTab === 'activities' && !isLoading}}">
    <view class="activity-list">
      <view class="activity-item" wx:for="{{activities}}" wx:key="id">
        <view class="activity-time">{{item.createdAt}}</view>
        <view class="activity-content">
          <view class="activity-type">{{item.activityTypeText || item.activityType}}</view>
          <view class="activity-detail" wx:if="{{item.planTitle}}">
            <text class="detail-label">学习计划:</text>
            <text class="detail-value">{{item.planTitle}}</text>
          </view>
          <view class="activity-detail" wx:if="{{item.contentType}}">
            <text class="detail-label">内容类型:</text>
            <text class="detail-value">{{item.contentType}}</text>
          </view>
          <view class="activity-detail" wx:if="{{item.duration}}">
            <text class="detail-label">持续时间:</text>
            <text class="detail-value">{{item.duration}}秒</text>
          </view>
        </view>
      </view>
      <view class="load-more" wx:if="{{hasMoreActivities && !isLoadingActivities}}" bindtap="loadMoreActivities">
        加载更多
      </view>
      <view class="loading" wx:if="{{isLoadingActivities}}">
        加载中...
      </view>
      <view class="empty-tip" wx:if="{{activities.length === 0 && !isLoadingActivities}}">
        暂无学习活动记录
      </view>
    </view>
  </view>

  <!-- 学习趋势标签页 -->
  <view class="tab-content {{isDarkMode ? 'dark-mode' : ''}}" wx:if="{{currentTab === 'trend' && !isLoading}}">
    <view class="trend-controls">
      <view class="trend-selector">
        <text>天数：</text>
        <picker mode="selector" range="{{[7, 14, 30, 60, 90]}}" value="{{2}}" bindchange="updateTrendDays">
          <view class="picker">
            {{trendDays}}天
          </view>
        </picker>
      </view>

      <!-- 图表类型切换 -->
      <view class="chart-type-selector">
        <view
          class="chart-type-btn {{chartType === 'bar' ? 'active' : ''}}"
          data-type="bar"
          bindtap="switchChartType"
        >
          <text class="chart-icon">📊</text>
        </view>
        <view
          class="chart-type-btn {{chartType === 'line' ? 'active' : ''}}"
          data-type="line"
          bindtap="switchChartType"
        >
          <text class="chart-icon">📈</text>
        </view>
        <view
          class="chart-type-btn {{chartType === 'area' ? 'active' : ''}}"
          data-type="area"
          bindtap="switchChartType"
        >
          <text class="chart-icon">📉</text>
        </view>
      </view>

      <!-- 数据标签开关 -->
      <view class="data-labels-toggle" bindtap="toggleDataLabels">
        <text class="toggle-label">数据标签</text>
        <view class="switch {{showDataLabels ? 'active' : ''}}">
          <view class="switch-thumb"></view>
        </view>
      </view>
    </view>

    <view class="trend-chart">
      <view class="chart-header">
        <text class="chart-title">学习时间趋势</text>
        <view class="chart-legend">
          <view class="legend-item">
            <view class="legend-color" style="background: {{chartColors[0]}}"></view>
            <text class="legend-text">学习时间</text>
          </view>
        </view>
      </view>

      <!-- 增强的趋势图表 -->
      <view class="trend-data chart-{{chartType}}">
        <view class="trend-item" wx:for="{{trendData}}" wx:key="date">
          <view wx:if="{{showDataLabels}}" class="trend-value-top">{{item.timeSpent}}</view>
          <view
            class="trend-bar"
            style="height: {{item.timeSpent > 0 ? (item.timeSpent > 100 ? 100 : item.timeSpent) : 5}}px; background: {{chartColors[0]}}"
          ></view>
          <view class="trend-date">{{item.date}}</view>
          <view wx:if="{{!showDataLabels}}" class="trend-value">{{item.timeSpent}}分钟</view>
        </view>
      </view>

      <!-- 图表网格线 -->
      <view class="chart-grid">
        <view class="grid-line" wx:for="{{[0, 25, 50, 75, 100]}}" wx:key="*this" style="bottom: {{item}}%">
          <text class="grid-label">{{item}}</text>
        </view>
      </view>
    </view>

    <view class="trend-summary">
      <view class="trend-summary-item">
        <text class="summary-label">平均学习时间：</text>
        <text class="summary-value">{{averageLearningTime}}分钟/天</text>
      </view>
      <view class="trend-summary-item">
        <text class="summary-label">最长学习时间：</text>
        <text class="summary-value">{{maxLearningTime}}分钟</text>
      </view>
      <view class="trend-summary-item">
        <text class="summary-label">有效学习天数：</text>
        <text class="summary-value">{{effectiveLearningDays}}/{{trendData.length}}天</text>
      </view>
    </view>

    <!-- 学习活动分布图表 -->
    <view wx:if="{{activityDistribution.length > 0}}" class="activity-distribution-chart">
      <view class="chart-header">
        <text class="chart-title">学习活动分布</text>
      </view>
      <view class="distribution-data">
        <view class="distribution-item" wx:for="{{activityDistribution}}" wx:key="type">
          <view class="activity-label">{{item.typeName}}</view>
          <view class="activity-bar-container">
            <view
              class="activity-bar"
              style="width: {{item.percentage}}%; background: {{chartColors[index % chartColors.length]}}"
            ></view>
            <text class="activity-percentage">{{item.percentage}}%</text>
          </view>
          <text class="activity-count">{{item.count}}次</text>
        </view>
      </view>
    </view>

    <!-- 学习时间段分析 -->
    <view wx:if="{{timeSlotAnalysis.length > 0}}" class="time-slot-analysis">
      <view class="chart-header">
        <text class="chart-title">学习时间段分析</text>
      </view>
      <view class="time-slot-data">
        <view class="time-slot-item" wx:for="{{timeSlotAnalysis}}" wx:key="hour">
          <view class="time-label">{{item.hour}}:00</view>
          <view
            class="time-bar"
            style="height: {{item.intensity * 100}}%; background: {{chartColors[1]}}"
          ></view>
          <view class="time-value">{{item.minutes}}分钟</view>
        </view>
      </view>
    </view>

    <!-- 数据导出和分享 -->
    <view class="chart-actions">
      <button class="action-btn export-btn" bindtap="exportLearningData">
        <text class="btn-icon">📊</text>
        <text class="btn-text">导出数据</text>
      </button>

      <button class="action-btn share-btn" open-type="share">
        <text class="btn-icon">📤</text>
        <text class="btn-text">分享统计</text>
      </button>

      <button class="action-btn timeline-btn" open-type="shareTimeline">
        <text class="btn-icon">🌐</text>
        <text class="btn-text">分享到朋友圈</text>
      </button>
    </view>

    <view class="empty-tip" wx:if="{{trendData.length === 0}}">
      暂无学习趋势数据
    </view>
  </view>
</view>

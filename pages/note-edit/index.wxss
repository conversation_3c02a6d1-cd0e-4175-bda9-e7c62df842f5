/* pages/note-edit/index.wxss */

/* 草稿状态提示 */
.draft-status {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 16rpx;
  margin: 20rpx 30rpx;
  border-left: 8rpx solid #0ea5e9;
}

.draft-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.draft-text {
  font-size: 28rpx;
  color: #0369a1;
  font-weight: 500;
  flex: 1;
}

.draft-time {
  font-size: 24rpx;
  color: #0284c7;
  opacity: 0.8;
}

/* 字数统计 */
.word-count {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24rpx;
  color: #94a3b8;
}

/* 编辑器工具栏 */
.editor-toolbar {
  padding: 20rpx 30rpx;
  background: #f8fafc;
  border-radius: 16rpx;
  margin: 20rpx 30rpx;
  border: 2rpx solid #e2e8f0;
}

.toolbar-group {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}

.toolbar-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  background: #ffffff;
  border: 2rpx solid #e2e8f0;
  border-radius: 12rpx;
  font-size: 24rpx;
  transition: all 0.2s ease;
}

.toolbar-btn:active {
  background: #f1f5f9;
  transform: scale(0.95);
}

.toolbar-icon {
  font-weight: bold;
  color: #475569;
}

/* 内容区域 */
.content-section {
  position: relative;
}

.content-edit {
  position: relative;
}

.content-input {
  min-height: 400rpx;
  padding: 30rpx;
  background: #ffffff;
  border-radius: 16rpx;
  margin: 20rpx 30rpx;
  border: 2rpx solid #e2e8f0;
  font-size: 32rpx;
  line-height: 1.6;
  color: #1e293b;
}

.content-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 6rpx rgba(59, 130, 246, 0.1);
}

.content-actions {
  display: flex;
  gap: 20rpx;
  padding: 20rpx 30rpx;
  justify-content: center;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 20rpx;
  background: #ffffff;
  border: 2rpx solid #e2e8f0;
  border-radius: 16rpx;
  min-width: 120rpx;
  transition: all 0.2s ease;
}

.action-btn:active {
  background: #f8fafc;
  transform: scale(0.95);
}

.action-icon {
  font-size: 32rpx;
}

.action-text {
  font-size: 24rpx;
  color: #64748b;
}

/* 预览模式 */
.content-preview {
  margin: 20rpx 30rpx;
  background: #ffffff;
  border-radius: 16rpx;
  border: 2rpx solid #e2e8f0;
  overflow: hidden;
}

.preview-content {
  padding: 30rpx;
  min-height: 400rpx;
}

.preview-text {
  font-size: 32rpx;
  line-height: 1.6;
  color: #1e293b;
  white-space: pre-wrap;
}

.preview-back-btn {
  width: 100%;
  padding: 24rpx;
  background: #f8fafc;
  border: none;
  border-top: 2rpx solid #e2e8f0;
  font-size: 28rpx;
  color: #3b82f6;
}

/* 字数统计栏 */
.word-count-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 30rpx;
  margin: 0 30rpx;
  background: #f8fafc;
  border-radius: 12rpx;
  border: 2rpx solid #e2e8f0;
}

.word-count-text {
  font-size: 24rpx;
  color: #64748b;
}

.save-status {
  font-size: 24rpx;
  color: #ef4444;
  padding: 8rpx 16rpx;
  background: #fef2f2;
  border-radius: 8rpx;
  border: 1rpx solid #fecaca;
}

.save-status.saved {
  color: #059669;
  background: #f0fdf4;
  border-color: #bbf7d0;
}

/* 图片管理 */
.images-section {
  margin: 30rpx;
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  margin-top: 20rpx;
}

.image-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 12rpx;
  overflow: hidden;
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
}

.image-thumb {
  width: 100%;
  height: 100%;
}

.image-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 16rpx 8rpx 8rpx;
  gap: 8rpx;
}

.image-action-btn {
  flex: 1;
  padding: 8rpx;
  font-size: 20rpx;
  border-radius: 6rpx;
  border: none;
  color: #ffffff;
}

.image-action-btn.insert {
  background: #3b82f6;
}

.image-action-btn.delete {
  background: #ef4444;
}

.image-item.uploading {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
}

.upload-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.upload-text {
  font-size: 24rpx;
  color: #64748b;
}

/* 底部按钮增强 */
.form-actions {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  background: #ffffff;
  border-top: 2rpx solid #e2e8f0;
  position: sticky;
  bottom: 0;
}

.cancel-btn {
  flex: 1;
  padding: 24rpx;
  background: #f8fafc;
  color: #64748b;
  border: 2rpx solid #e2e8f0;
  border-radius: 12rpx;
  font-size: 28rpx;
}

.draft-btn {
  flex: 1;
  padding: 24rpx;
  background: #fef3c7;
  color: #d97706;
  border: 2rpx solid #fbbf24;
  border-radius: 12rpx;
  font-size: 28rpx;
}

.save-btn {
  flex: 2;
  padding: 24rpx;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: #ffffff;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.save-btn.disabled {
  background: #94a3b8;
  opacity: 0.6;
}
.note-edit-container {
  width: 100%;
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 120rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid rgba(74, 144, 226, 0.1);
  border-top-color: #4a90e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner.small {
  width: 40rpx;
  height: 40rpx;
  border-width: 4rpx;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 编辑表单 */
.edit-form {
  padding: 30rpx;
}

.form-item {
  margin-bottom: 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

/* 标题输入 */
.title-input {
  width: 100%;
  height: 80rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  padding: 0 10rpx;
}

/* 内容输入 */
.content-input {
  width: 100%;
  min-height: 400rpx;
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  padding: 10rpx;
}

/* 标签容器 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.tag-item {
  display: flex;
  align-items: center;
  background-color: rgba(74, 144, 226, 0.1);
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
}

.tag-name {
  font-size: 24rpx;
  color: #4a90e2;
  margin-right: 10rpx;
}

.tag-remove {
  font-size: 28rpx;
  color: #4a90e2;
  font-weight: bold;
}

.add-tag {
  display: flex;
  align-items: center;
  background-color: #f0f0f0;
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
}

.add-icon {
  font-size: 24rpx;
  color: #999;
  margin-right: 6rpx;
}

.add-text {
  font-size: 24rpx;
  color: #999;
}

/* 公开设置 */
.public-switch {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.switch-label {
  font-size: 28rpx;
  color: #333;
}

.switch-container {
  width: 80rpx;
  height: 40rpx;
  background-color: #e0e0e0;
  border-radius: 20rpx;
  position: relative;
  transition: background-color 0.3s;
}

.switch-container.active {
  background-color: #4a90e2;
}

.switch-handle {
  width: 36rpx;
  height: 36rpx;
  background-color: #fff;
  border-radius: 50%;
  position: absolute;
  top: 2rpx;
  left: 2rpx;
  transition: transform 0.3s;
}

.switch-container.active .switch-handle {
  transform: translateX(40rpx);
}

.public-hint {
  font-size: 24rpx;
  color: #999;
}

/* 底部按钮 */
.form-actions {
  display: flex;
  justify-content: space-between;
  padding: 30rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.cancel-btn, .save-btn {
  width: 45%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.cancel-btn {
  background-color: #f0f0f0;
  color: #666;
}

.save-btn {
  background-color: #4a90e2;
  color: #fff;
}

.save-btn.disabled {
  background-color: #a0c8f0;
}

.btn-hover {
  opacity: 0.8;
}

/* 标签选择器 */
.tag-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.tag-selector-container {
  width: 80%;
  max-height: 70vh;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eee;
}

.selector-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.selector-close {
  font-size: 40rpx;
  color: #999;
}

.selector-content {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.selector-tag-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f0f0f0;
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
  min-width: 120rpx;
}

.selector-tag-item.selected {
  background-color: rgba(74, 144, 226, 0.1);
}

.selector-tag-name {
  font-size: 24rpx;
  color: #666;
}

.selector-tag-item.selected .selector-tag-name {
  color: #4a90e2;
}

.selector-tag-check {
  font-size: 24rpx;
  color: #4a90e2;
  margin-left: 10rpx;
}

.empty-tags {
  width: 100%;
  padding: 40rpx 0;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}

.loading-tags {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 0;
}

.selector-footer {
  padding: 20rpx;
  border-top: 1rpx solid #eee;
}

.confirm-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #4a90e2;
  color: #fff;
  border-radius: 40rpx;
  font-size: 28rpx;
}

// pages/note-edit/index.js
// 笔记创建/编辑页面

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 笔记ID，编辑模式下有值，创建模式下为空
    noteId: '',
    // 笔记标题
    title: '',
    // 笔记内容
    content: '',
    // 关联标签ID
    tagId: '',
    // 关联标签列表
    tags: [],
    // 是否公开
    isPublic: true,
    // 是否为编辑模式
    isEditMode: false,
    // 是否正在加载
    isLoading: false,
    // 是否正在提交
    isSubmitting: false,
    // 是否显示标签选择器
    showTagSelector: false,
    // 可选标签列表
    availableTags: [],
    // 是否正在加载标签
    isLoadingTags: false,

    // 新增功能相关数据
    // 富文本编辑器相关
    showPreview: false, // 是否显示预览模式
    editorMode: 'edit', // 'edit' 或 'preview'

    // 图片相关
    images: [], // 已上传的图片列表
    isUploadingImage: false, // 是否正在上传图片

    // 草稿相关
    isDraft: false, // 是否为草稿
    lastSaveTime: null, // 最后保存时间
    autoSaveTimer: null, // 自动保存定时器
    hasUnsavedChanges: false, // 是否有未保存的更改

    // 编辑器工具栏
    showToolbar: false, // 是否显示工具栏
    cursorPosition: 0, // 光标位置

    // 字数统计
    wordCount: 0, // 字数统计

    // 分类相关
    selectedCategory: '', // 选中的分类
    categories: [], // 可选分类列表

    // 封面图片
    coverImage: '', // 封面图片URL
    showCoverSelector: false // 是否显示封面选择器
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '创建笔记'
    });

    // 检查是否有笔记ID参数（编辑模式）
    if (options.id) {
      this.setData({
        noteId: options.id,
        isEditMode: true
      });

      // 设置导航栏标题为编辑模式
      wx.setNavigationBarTitle({
        title: '编辑笔记'
      });

      // 加载笔记数据
      this.loadNoteData(options.id);
    } else {
      // 创建模式，检查是否有草稿
      setTimeout(() => {
        this.loadDraft();
      }, 500);
    }

    // 检查是否有标签ID参数
    if (options.tagId) {
      this.setData({
        tagId: options.tagId
      });
    }

    // 加载可用标签
    this.loadAvailableTags();

    // 初始化字数统计
    this.setData({
      wordCount: this.data.content.length
    });
  },

  /**
   * 页面显示时
   */
  onShow: function () {
    // 页面显示时启动自动保存
    if (this.data.hasUnsavedChanges) {
      this.startAutoSave();
    }
  },

  /**
   * 页面隐藏时
   */
  onHide: function () {
    // 页面隐藏时保存草稿
    if (this.data.hasUnsavedChanges) {
      this.saveDraft();
    }

    // 清除自动保存定时器
    if (this.data.autoSaveTimer) {
      clearTimeout(this.data.autoSaveTimer);
    }
  },

  /**
   * 页面卸载时
   */
  onUnload: function () {
    // 页面卸载时保存草稿
    if (this.data.hasUnsavedChanges) {
      this.saveDraft();
    }

    // 清除自动保存定时器
    if (this.data.autoSaveTimer) {
      clearTimeout(this.data.autoSaveTimer);
    }
  },

  /**
   * 加载笔记数据
   * @param {string} noteId 笔记ID
   */
  loadNoteData: function (noteId) {
    this.setData({ isLoading: true });

    // 获取API客户端
    const app = getApp();
    const api = app.globalData.api;

    if (!api) {
      console.error('API客户端未初始化');
      this.setData({ isLoading: false });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
      return;
    }

    // 调用API获取笔记详情
    api.note.getNote(noteId)
      .then(res => {
        console.log('笔记数据:', res);
        if (res.success && res.data) {
          const note = res.data;
          this.setData({
            title: note.title || '',
            content: note.content || '',
            isPublic: note.isPublic || false,
            tags: note.tags || [],
            isLoading: false
          });
        } else {
          throw new Error('获取笔记数据失败');
        }
      })
      .catch(err => {
        console.error('加载笔记数据失败:', err);
        this.setData({ isLoading: false });
        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      });
  },

  /**
   * 加载可用标签
   */
  loadAvailableTags: function () {
    this.setData({ isLoadingTags: true });

    // 获取API客户端
    const app = getApp();
    const api = app.globalData.api;

    if (!api) {
      console.error('API客户端未初始化');
      this.setData({ isLoadingTags: false });
      return;
    }

    // 调用API获取标签列表
    api.tag.getTags()
      .then(res => {
        console.log('标签数据:', res);
        if (res.success && res.data && res.data.tags) {
          this.setData({
            availableTags: res.data.tags,
            isLoadingTags: false
          });

          // 如果有tagId参数，设置为已选标签
          if (this.data.tagId) {
            const selectedTag = res.data.tags.find(tag => tag.id === this.data.tagId);
            if (selectedTag) {
              this.setData({
                tags: [selectedTag]
              });
            }
          }
        } else {
          throw new Error('获取标签数据失败');
        }
      })
      .catch(err => {
        console.error('加载标签数据失败:', err);
        this.setData({ isLoadingTags: false });
      });
  },

  /**
   * 处理标题输入
   */
  handleTitleInput: function (e) {
    this.setData({
      title: e.detail.value
    });
  },

  /**
   * 处理内容输入
   */
  handleContentInput: function (e) {
    const content = e.detail.value;
    const wordCount = content.length;

    this.setData({
      content: content,
      wordCount: wordCount,
      hasUnsavedChanges: true
    });

    // 启动自动保存
    this.startAutoSave();
  },

  /**
   * 启动自动保存
   */
  startAutoSave: function () {
    // 清除之前的定时器
    if (this.data.autoSaveTimer) {
      clearTimeout(this.data.autoSaveTimer);
    }

    // 设置新的自动保存定时器（30秒后保存）
    const timer = setTimeout(() => {
      this.saveDraft();
    }, 30000);

    this.setData({
      autoSaveTimer: timer
    });
  },

  /**
   * 保存草稿
   */
  saveDraft: function () {
    if (!this.data.hasUnsavedChanges) return;

    const draftData = {
      title: this.data.title,
      content: this.data.content,
      tags: this.data.tags,
      isPublic: this.data.isPublic,
      images: this.data.images,
      coverImage: this.data.coverImage,
      timestamp: Date.now()
    };

    // 保存到本地存储
    const draftKey = this.data.noteId ? `draft_${this.data.noteId}` : 'draft_new_note';
    wx.setStorageSync(draftKey, draftData);

    this.setData({
      lastSaveTime: new Date().toLocaleTimeString(),
      hasUnsavedChanges: false,
      isDraft: true
    });

    console.log('草稿已自动保存');
  },

  /**
   * 加载草稿
   */
  loadDraft: function () {
    const draftKey = this.data.noteId ? `draft_${this.data.noteId}` : 'draft_new_note';
    const draftData = wx.getStorageSync(draftKey);

    if (draftData && draftData.timestamp) {
      // 检查草稿是否在24小时内
      const now = Date.now();
      const draftAge = now - draftData.timestamp;
      const maxAge = 24 * 60 * 60 * 1000; // 24小时

      if (draftAge < maxAge) {
        wx.showModal({
          title: '发现草稿',
          content: '检测到未保存的草稿，是否恢复？',
          success: (res) => {
            if (res.confirm) {
              this.setData({
                title: draftData.title || '',
                content: draftData.content || '',
                tags: draftData.tags || [],
                isPublic: draftData.isPublic !== undefined ? draftData.isPublic : true,
                images: draftData.images || [],
                coverImage: draftData.coverImage || '',
                wordCount: (draftData.content || '').length,
                isDraft: true,
                lastSaveTime: new Date(draftData.timestamp).toLocaleTimeString()
              });
            } else {
              // 用户选择不恢复，删除草稿
              wx.removeStorageSync(draftKey);
            }
          }
        });
      } else {
        // 草稿过期，删除
        wx.removeStorageSync(draftKey);
      }
    }
  },

  /**
   * 清除草稿
   */
  clearDraft: function () {
    const draftKey = this.data.noteId ? `draft_${this.data.noteId}` : 'draft_new_note';
    wx.removeStorageSync(draftKey);

    this.setData({
      isDraft: false,
      lastSaveTime: null,
      hasUnsavedChanges: false
    });
  },

  /**
   * 切换是否公开
   */
  togglePublic: function () {
    this.setData({
      isPublic: !this.data.isPublic
    });
  },

  /**
   * 显示标签选择器
   */
  showTagSelector: function () {
    this.setData({
      showTagSelector: true
    });
  },

  /**
   * 隐藏标签选择器
   */
  hideTagSelector: function () {
    this.setData({
      showTagSelector: false
    });
  },

  /**
   * 选择标签
   */
  selectTag: function (e) {
    const tagId = e.currentTarget.dataset.id;
    const tag = this.data.availableTags.find(t => t.id === tagId);
    
    if (tag) {
      // 检查标签是否已选择
      const isSelected = this.data.tags.some(t => t.id === tagId);
      
      if (isSelected) {
        // 如果已选择，则移除
        this.setData({
          tags: this.data.tags.filter(t => t.id !== tagId)
        });
      } else {
        // 如果未选择，则添加
        this.setData({
          tags: [...this.data.tags, tag]
        });
      }
    }
  },

  /**
   * 移除标签
   */
  removeTag: function (e) {
    const tagId = e.currentTarget.dataset.id;
    this.setData({
      tags: this.data.tags.filter(t => t.id !== tagId),
      hasUnsavedChanges: true
    });
  },

  /**
   * 选择并上传图片
   */
  selectImage: function () {
    wx.chooseImage({
      count: 9 - this.data.images.length, // 最多9张图片
      sizeType: ['compressed'], // 使用压缩图
      sourceType: ['album', 'camera'],
      success: (res) => {
        this.uploadImages(res.tempFilePaths);
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 上传图片
   */
  uploadImages: function (tempFilePaths) {
    if (tempFilePaths.length === 0) return;

    this.setData({ isUploadingImage: true });

    const uploadPromises = tempFilePaths.map(filePath => {
      return new Promise((resolve, reject) => {
        // 获取API客户端
        const app = getApp();
        const api = app.globalData.api;

        if (!api) {
          reject(new Error('API客户端未初始化'));
          return;
        }

        // 上传图片
        wx.uploadFile({
          url: `${api.baseUrl}/upload/image`,
          filePath: filePath,
          name: 'image',
          header: {
            'Authorization': `Bearer ${wx.getStorageSync('token')}`
          },
          success: (res) => {
            try {
              const data = JSON.parse(res.data);
              if (data.success) {
                resolve({
                  url: data.data.url,
                  tempPath: filePath
                });
              } else {
                reject(new Error(data.message || '上传失败'));
              }
            } catch (err) {
              reject(new Error('解析响应失败'));
            }
          },
          fail: (err) => {
            reject(err);
          }
        });
      });
    });

    Promise.all(uploadPromises)
      .then(results => {
        const newImages = [...this.data.images, ...results];
        this.setData({
          images: newImages,
          isUploadingImage: false,
          hasUnsavedChanges: true
        });

        wx.showToast({
          title: '图片上传成功',
          icon: 'success'
        });
      })
      .catch(err => {
        console.error('上传图片失败:', err);
        this.setData({ isUploadingImage: false });

        wx.showToast({
          title: '图片上传失败',
          icon: 'none'
        });
      });
  },

  /**
   * 删除图片
   */
  removeImage: function (e) {
    const index = e.currentTarget.dataset.index;
    const images = [...this.data.images];
    images.splice(index, 1);

    this.setData({
      images: images,
      hasUnsavedChanges: true
    });
  },

  /**
   * 预览图片
   */
  previewImage: function (e) {
    const index = e.currentTarget.dataset.index;
    const urls = this.data.images.map(img => img.url);

    wx.previewImage({
      current: urls[index],
      urls: urls
    });
  },

  /**
   * 插入图片到内容中
   */
  insertImageToContent: function (e) {
    const index = e.currentTarget.dataset.index;
    const image = this.data.images[index];

    if (image) {
      const imageMarkdown = `\n![图片](${image.url})\n`;
      const content = this.data.content + imageMarkdown;

      this.setData({
        content: content,
        wordCount: content.length,
        hasUnsavedChanges: true
      });
    }
  },

  /**
   * 切换编辑器模式
   */
  toggleEditorMode: function () {
    const newMode = this.data.editorMode === 'edit' ? 'preview' : 'edit';
    this.setData({
      editorMode: newMode,
      showPreview: newMode === 'preview'
    });
  },

  /**
   * 显示/隐藏工具栏
   */
  toggleToolbar: function () {
    this.setData({
      showToolbar: !this.data.showToolbar
    });
  },

  /**
   * 插入格式化文本
   */
  insertFormat: function (e) {
    const format = e.currentTarget.dataset.format;
    const content = this.data.content;
    const cursorPos = this.data.cursorPosition;

    let insertText = '';
    let newCursorPos = cursorPos;

    switch (format) {
      case 'bold':
        insertText = '**粗体文字**';
        newCursorPos = cursorPos + 2;
        break;
      case 'italic':
        insertText = '*斜体文字*';
        newCursorPos = cursorPos + 1;
        break;
      case 'heading':
        insertText = '\n## 标题\n';
        newCursorPos = cursorPos + 3;
        break;
      case 'list':
        insertText = '\n- 列表项\n';
        newCursorPos = cursorPos + 3;
        break;
      case 'link':
        insertText = '[链接文字](链接地址)';
        newCursorPos = cursorPos + 1;
        break;
      case 'quote':
        insertText = '\n> 引用文字\n';
        newCursorPos = cursorPos + 3;
        break;
    }

    const newContent = content.slice(0, cursorPos) + insertText + content.slice(cursorPos);

    this.setData({
      content: newContent,
      wordCount: newContent.length,
      cursorPosition: newCursorPos,
      hasUnsavedChanges: true
    });
  },

  /**
   * 保存笔记
   */
  saveNote: function () {
    // 验证表单
    if (!this.data.title.trim()) {
      wx.showToast({
        title: '请输入标题',
        icon: 'none'
      });
      return;
    }

    if (!this.data.content.trim()) {
      wx.showToast({
        title: '请输入内容',
        icon: 'none'
      });
      return;
    }

    this.setData({ isSubmitting: true });

    // 获取API客户端
    const app = getApp();
    const api = app.globalData.api;

    if (!api) {
      console.error('API客户端未初始化');
      this.setData({ isSubmitting: false });
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
      return;
    }

    // 构建笔记数据
    const noteData = {
      title: this.data.title,
      content: this.data.content,
      isPublic: this.data.isPublic,
      tagIds: this.data.tags.map(tag => tag.id),
      images: this.data.images.map(img => img.url),
      coverImage: this.data.coverImage,
      isDraft: false // 正式保存时设为非草稿
    };

    // 根据模式调用不同的API
    const apiPromise = this.data.isEditMode
      ? api.note.updateNote(this.data.noteId, noteData)
      : api.note.createNote(noteData);

    apiPromise
      .then(res => {
        console.log('保存笔记结果:', res);
        if (res.success) {
          // 清除草稿
          this.clearDraft();

          wx.showToast({
            title: this.data.isEditMode ? '更新成功' : '创建成功',
            icon: 'success'
          });

          // 延迟返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        } else {
          throw new Error('保存笔记失败');
        }
      })
      .catch(err => {
        console.error('保存笔记失败:', err);
        wx.showToast({
          title: '保存失败，请重试',
          icon: 'none'
        });
      })
      .finally(() => {
        this.setData({ isSubmitting: false });
      });
  },

  /**
   * 取消编辑
   */
  cancelEdit: function () {
    wx.showModal({
      title: '确认取消',
      content: '确定要放弃当前编辑吗？',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack();
        }
      }
    });
  }
});

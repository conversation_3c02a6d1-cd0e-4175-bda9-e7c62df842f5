/* pages/note-detail/index.wxss */

/* 点赞动画效果 */
.action-button.animating {
  animation: likeScale 0.3s ease-in-out;
}

@keyframes likeScale {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

.like-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

.particle {
  position: absolute;
  font-size: 24rpx;
  animation: particleFloat 1s ease-out forwards;
  opacity: 0;
}

@keyframes particleFloat {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateY(-100rpx) scale(0.5);
  }
}

/* 评论操作增强 */
.comment-actions {
  display: flex;
  gap: 32rpx;
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #f1f5f9;
}

.comment-action {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: #f8fafc;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #64748b;
  transition: all 0.2s ease;
}

.comment-action:active {
  background: #e2e8f0;
  transform: scale(0.95);
}

.comment-action .action-icon {
  font-size: 28rpx;
}

.comment-action .action-text {
  font-size: 22rpx;
}

/* 回复列表 */
.reply-list {
  margin-top: 24rpx;
  padding-left: 60rpx;
  border-left: 4rpx solid #e2e8f0;
}

.reply-item {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
  padding: 16rpx;
  background: #f8fafc;
  border-radius: 12rpx;
}

.reply-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  flex-shrink: 0;
}

.reply-content {
  flex: 1;
  min-width: 0;
}

.reply-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.reply-author {
  font-size: 26rpx;
  font-weight: 500;
  color: #374151;
}

.reply-time {
  font-size: 22rpx;
  color: #94a3b8;
}

.reply-text {
  font-size: 28rpx;
  line-height: 1.5;
  color: #1e293b;
}

/* 评论弹窗增强 */
.reply-hint {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 30rpx;
  background: #eff6ff;
  border-bottom: 2rpx solid #e0e7ff;
}

.hint-text {
  font-size: 26rpx;
  color: #3730a3;
}

.hint-close {
  font-size: 32rpx;
  color: #6b7280;
  padding: 8rpx;
}

.emoji-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 2rpx solid #f1f5f9;
}

.emoji-toggle {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 12rpx 20rpx;
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #64748b;
}

.emoji-icon {
  font-size: 32rpx;
}

.emoji-text {
  font-size: 24rpx;
}

.emoji-panel {
  margin-top: 20rpx;
  padding: 20rpx;
  background: #f8fafc;
  border-radius: 16rpx;
  border: 2rpx solid #e2e8f0;
  max-height: 300rpx;
  overflow-y: auto;
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 16rpx;
}

.emoji-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  font-size: 32rpx;
  background: #ffffff;
  border-radius: 12rpx;
  border: 2rpx solid #e2e8f0;
  transition: all 0.2s ease;
}

.emoji-item:active {
  background: #e2e8f0;
  transform: scale(0.9);
}

/* 分享弹窗增强 */
.qrcode-section {
  padding: 40rpx 30rpx;
  text-align: center;
}

.qrcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}

.qrcode-image {
  width: 400rpx;
  height: 400rpx;
  border-radius: 16rpx;
  border: 2rpx solid #e2e8f0;
  background: #ffffff;
}

.qrcode-hint {
  font-size: 28rpx;
  color: #64748b;
}

.save-qrcode-btn {
  padding: 20rpx 40rpx;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: #ffffff;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.share-options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  padding: 30rpx;
}

.share-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  padding: 32rpx 20rpx;
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 16rpx;
  transition: all 0.2s ease;
}

.share-option:active {
  background: #e2e8f0;
  transform: scale(0.95);
}

.share-icon {
  font-size: 48rpx;
}

.share-text {
  font-size: 26rpx;
  color: #374151;
  font-weight: 500;
}

/* 收藏夹弹窗 */
.collection-modal {
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  background: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
}

.create-collection {
  padding: 30rpx;
}

.collection-name-input {
  width: 100%;
  padding: 24rpx;
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 12rpx;
  font-size: 28rpx;
  margin-bottom: 24rpx;
}

.create-actions {
  display: flex;
  gap: 20rpx;
}

.cancel-create-btn, .confirm-create-btn {
  flex: 1;
  padding: 20rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
}

.cancel-create-btn {
  background: #f8fafc;
  color: #64748b;
  border: 2rpx solid #e2e8f0;
}

.confirm-create-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: #ffffff;
}

.collection-list {
  max-height: 400rpx;
  overflow-y: auto;
  padding: 0 30rpx 30rpx;
}

.collection-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  margin-bottom: 16rpx;
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 16rpx;
  transition: all 0.2s ease;
}

.collection-item:active {
  background: #e2e8f0;
  transform: scale(0.98);
}

.collection-item.selected {
  background: #eff6ff;
  border-color: #3b82f6;
}

.collection-item.create-new {
  border-style: dashed;
  border-color: #94a3b8;
}

.collection-info {
  flex: 1;
}

.collection-name {
  display: block;
  font-size: 30rpx;
  font-weight: 500;
  color: #1e293b;
  margin-bottom: 8rpx;
}

.collection-count {
  font-size: 24rpx;
  color: #64748b;
}

.collection-check {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f1f5f9;
  border: 2rpx solid #e2e8f0;
}

.collection-item.selected .collection-check {
  background: #3b82f6;
  border-color: #3b82f6;
}

.check-icon {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: bold;
}

.container {
  min-height: 100vh;
  background-color: var(--bg-color-primary, #f8f9fa);
  color: var(--text-color-primary, #333);
}

.dark-mode {
  background-color: var(--bg-color-primary-dark, #1a1a1a);
  color: var(--text-color-primary-dark, #fff);
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e0e0e0;
  border-top: 4rpx solid var(--primary-color, #3775f5);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  color: var(--text-color-secondary, #666);
  font-size: 28rpx;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.error-text {
  color: var(--text-color-secondary, #666);
  font-size: 32rpx;
  margin-bottom: 40rpx;
}

.retry-button {
  background-color: var(--primary-color, #3775f5);
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 笔记内容 */
.note-content {
  padding: 0 32rpx 40rpx;
}

/* 笔记头部 */
.note-header {
  padding: 40rpx 0 32rpx;
  border-bottom: 1rpx solid var(--border-color, #e0e0e0);
}

.note-title {
  font-size: 44rpx;
  font-weight: 600;
  line-height: 1.4;
  margin-bottom: 24rpx;
  color: var(--text-color-primary, #333);
}

.note-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.author-info {
  display: flex;
  align-items: center;
}

.author-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}

.author-details {
  display: flex;
  flex-direction: column;
}

.author-name {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-color-primary, #333);
  margin-bottom: 4rpx;
}

.publish-time {
  font-size: 24rpx;
  color: var(--text-color-secondary, #666);
}

.note-stats {
  display: flex;
  gap: 16rpx;
}

.stat-item {
  font-size: 24rpx;
  color: var(--text-color-secondary, #666);
}

/* 标签 */
.note-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin: 32rpx 0;
}

.tag-item {
  background-color: var(--tag-bg-color, #f0f2f5);
  color: var(--primary-color, #3775f5);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: 1rpx solid var(--primary-color, #3775f5);
}

/* 笔记正文 */
.note-body {
  margin: 32rpx 0;
}

.note-images {
  margin-bottom: 32rpx;
}

.note-image {
  width: 100%;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.note-text {
  line-height: 1.8;
}

.content-text {
  font-size: 32rpx;
  color: var(--text-color-primary, #333);
  white-space: pre-wrap;
}

/* 操作按钮栏 */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 0;
  border-top: 1rpx solid var(--border-color, #e0e0e0);
  border-bottom: 1rpx solid var(--border-color, #e0e0e0);
  margin: 32rpx 0;
}

.action-left,
.action-right {
  display: flex;
  gap: 32rpx;
}

.action-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.action-button:active {
  background-color: var(--bg-color-secondary, #f0f0f0);
}

.action-icon {
  font-size: 32rpx;
}

.action-text {
  font-size: 24rpx;
  color: var(--text-color-secondary, #666);
}

.action-button.liked .action-text,
.action-button.collected .action-text {
  color: var(--primary-color, #3775f5);
}

.action-button.danger .action-text {
  color: var(--danger-color, #e64340);
}

/* 评论区域 */
.comments-section,
.related-section {
  margin-top: 40rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 24rpx;
  color: var(--text-color-primary, #333);
}

.comment-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.comment-item {
  display: flex;
  gap: 16rpx;
}

.comment-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  flex-shrink: 0;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.comment-author {
  font-size: 26rpx;
  font-weight: 500;
  color: var(--text-color-primary, #333);
}

.comment-time {
  font-size: 22rpx;
  color: var(--text-color-secondary, #666);
}

.comment-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: var(--text-color-primary, #333);
}

/* 相关笔记 */
.related-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.related-item {
  display: flex;
  gap: 16rpx;
  padding: 16rpx;
  border-radius: 12rpx;
  background-color: var(--bg-color-secondary, #fff);
  border: 1rpx solid var(--border-color, #e0e0e0);
}

.related-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  flex-shrink: 0;
}

.related-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 8rpx;
}

.related-title {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-color-primary, #333);
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.related-author {
  font-size: 24rpx;
  color: var(--text-color-secondary, #666);
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.comment-modal,
.share-modal {
  background-color: var(--bg-color-secondary, #fff);
  border-radius: 16rpx;
  margin: 32rpx;
  max-height: 80vh;
  width: calc(100% - 64rpx);
  max-width: 600rpx;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid var(--border-color, #e0e0e0);
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-color-primary, #333);
}

.modal-close {
  font-size: 32rpx;
  color: var(--text-color-secondary, #666);
  padding: 8rpx;
}

.modal-body {
  padding: 32rpx;
}

.comment-input {
  width: 100%;
  min-height: 200rpx;
  padding: 16rpx;
  border: 1rpx solid var(--border-color, #e0e0e0);
  border-radius: 8rpx;
  font-size: 28rpx;
  line-height: 1.6;
  resize: none;
}

.input-counter {
  text-align: right;
  margin-top: 8rpx;
  font-size: 24rpx;
  color: var(--text-color-secondary, #666);
}

.modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 1rpx solid var(--border-color, #e0e0e0);
}

.cancel-button,
.submit-button {
  flex: 1;
  padding: 20rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
}

.cancel-button {
  background-color: var(--bg-color-secondary, #f0f0f0);
  color: var(--text-color-secondary, #666);
}

.submit-button {
  background-color: var(--primary-color, #3775f5);
  color: white;
}

.submit-button.active {
  opacity: 1;
}

.submit-button:not(.active) {
  opacity: 0.5;
}

/* 分享选项 */
.share-options {
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.share-option {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx;
  border: 1rpx solid var(--border-color, #e0e0e0);
  border-radius: 8rpx;
  background-color: transparent;
  font-size: 28rpx;
}

.share-icon {
  font-size: 32rpx;
}

.share-text {
  color: var(--text-color-primary, #333);
}

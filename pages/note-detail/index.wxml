<!--pages/note-detail/index.wxml-->
<view class="container {{isDarkMode ? 'dark-mode' : ''}}">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 加载失败状态 -->
  <view class="error-container" wx:if="{{loadingFailed}}">
    <view class="error-icon">⚠️</view>
    <text class="error-text">加载失败</text>
    <button class="retry-button" bindtap="retryLoading">重试</button>
  </view>

  <!-- 笔记内容 -->
  <view class="note-content" wx:if="{{!isLoading && !loadingFailed && note}}">
    <!-- 笔记头部 -->
    <view class="note-header">
      <view class="note-title">{{note.title}}</view>
      <view class="note-meta">
        <view class="author-info">
          <image class="author-avatar" src="{{note.userAvatar}}" mode="aspectFill"></image>
          <view class="author-details">
            <text class="author-name">{{note.userName}}</text>
            <text class="publish-time">{{note.createdAt}}</text>
          </view>
        </view>
        <view class="note-stats">
          <text class="stat-item">{{note.viewCount || 0}} 阅读</text>
          <text class="stat-item">{{note.likeCount || 0}} 点赞</text>
          <text class="stat-item">{{note.commentCount || 0}} 评论</text>
        </view>
      </view>
    </view>

    <!-- 笔记标签 -->
    <view class="note-tags" wx:if="{{note.tags && note.tags.length > 0}}">
      <view class="tag-item" wx:for="{{note.tags}}" wx:key="id">
        {{item.name}}
      </view>
    </view>

    <!-- 笔记正文 -->
    <view class="note-body">
      <!-- 笔记图片 -->
      <view class="note-images" wx:if="{{note.imageUrls && note.imageUrls.length > 0}}">
        <image 
          class="note-image" 
          wx:for="{{note.imageUrls}}" 
          wx:key="*this"
          src="{{item}}" 
          mode="widthFix"
          lazy-load="true"
          bindtap="previewImage"
          data-url="{{item}}"
          data-urls="{{note.imageUrls}}">
        </image>
      </view>

      <!-- 笔记文本内容 -->
      <view class="note-text">
        <text class="content-text">{{note.content}}</text>
      </view>
    </view>

    <!-- 操作按钮栏 -->
    <view class="action-bar">
      <view class="action-left">
        <view class="action-button {{isLiked ? 'liked' : ''}} {{likeAnimation ? 'animating' : ''}}" bindtap="toggleLike">
          <view class="action-content">
            <text class="action-icon">{{isLiked ? '❤️' : '🤍'}}</text>
            <text class="action-text">{{note.likeCount || 0}}</text>

            <!-- 点赞粒子效果 -->
            <view class="like-particles" wx:if="{{likeParticles.length > 0}}">
              <view
                class="particle"
                wx:for="{{likeParticles}}"
                wx:key="id"
                style="left: {{item.x}}%; top: {{item.y}}%; animation-delay: {{item.delay}}ms;"
              >❤️</view>
            </view>
          </view>
        </view>

        <view class="action-button" bindtap="showCommentModal">
          <text class="action-icon">💬</text>
          <text class="action-text">{{comments.length || 0}}</text>
        </view>

        <view class="action-button {{isCollected ? 'collected' : ''}}" bindtap="showCollectionModal">
          <text class="action-icon">{{isCollected ? '⭐' : '☆'}}</text>
          <text class="action-text">{{isCollected ? '已收藏' : '收藏'}}</text>
        </view>
      </view>

      <view class="action-right">
        <view class="action-button" bindtap="showShareModal">
          <text class="action-icon">📤</text>
          <text class="action-text">分享</text>
        </view>

        <view class="action-button" wx:if="{{isOwner}}" bindtap="editNote">
          <text class="action-icon">✏️</text>
          <text class="action-text">编辑</text>
        </view>

        <view class="action-button danger" wx:if="{{isOwner}}" bindtap="deleteNote">
          <text class="action-icon">🗑️</text>
          <text class="action-text">删除</text>
        </view>
      </view>
    </view>

    <!-- 评论列表 -->
    <view class="comments-section" wx:if="{{comments.length > 0}}">
      <view class="section-title">评论 ({{comments.length}})</view>
      <view class="comment-list">
        <view class="comment-item" wx:for="{{comments}}" wx:key="id">
          <image class="comment-avatar" src="{{item.userAvatar}}" mode="aspectFill"></image>
          <view class="comment-content">
            <view class="comment-header">
              <text class="comment-author">{{item.userName}}</text>
              <text class="comment-time">{{item.createdAt}}</text>
            </view>
            <view class="comment-text">{{item.content}}</view>

            <!-- 评论操作 -->
            <view class="comment-actions">
              <view class="comment-action" bindtap="replyToComment" data-comment="{{item}}">
                <text class="action-icon">💬</text>
                <text class="action-text">回复</text>
              </view>
              <view class="comment-action" bindtap="likeComment" data-id="{{item.id}}">
                <text class="action-icon">{{item.isLiked ? '❤️' : '🤍'}}</text>
                <text class="action-text">{{item.likeCount || 0}}</text>
              </view>
            </view>

            <!-- 回复列表 -->
            <view class="reply-list" wx:if="{{item.replies && item.replies.length > 0}}">
              <view class="reply-item" wx:for="{{item.replies}}" wx:key="id" wx:for-item="reply">
                <image class="reply-avatar" src="{{reply.userAvatar}}" mode="aspectFill"></image>
                <view class="reply-content">
                  <view class="reply-header">
                    <text class="reply-author">{{reply.userName}}</text>
                    <text class="reply-time">{{reply.createdAt}}</text>
                  </view>
                  <view class="reply-text">{{reply.content}}</view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 相关笔记 -->
    <view class="related-section" wx:if="{{relatedNotes.length > 0}}">
      <view class="section-title">相关笔记</view>
      <view class="related-list">
        <view 
          class="related-item" 
          wx:for="{{relatedNotes}}" 
          wx:key="id"
          bindtap="viewRelatedNote"
          data-id="{{item.id}}">
          <image class="related-image" src="{{item.imageUrl}}" mode="aspectFill"></image>
          <view class="related-info">
            <text class="related-title">{{item.title}}</text>
            <text class="related-author">{{item.userName}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 评论弹窗 -->
  <view class="modal-overlay" wx:if="{{showCommentModal}}" bindtap="hideCommentModal">
    <view class="comment-modal" catchtap="">
      <view class="modal-header">
        <text class="modal-title">{{replyToComment ? '回复评论' : '添加评论'}}</text>
        <text class="modal-close" bindtap="hideCommentModal">✕</text>
      </view>

      <!-- 回复提示 -->
      <view class="reply-hint" wx:if="{{replyToComment}}">
        <text class="hint-text">回复 @{{replyToComment.userName}}</text>
        <text class="hint-close" bindtap="clearReply">✕</text>
      </view>

      <view class="modal-body">
        <textarea
          class="comment-input"
          placeholder="{{replyToComment ? '写下你的回复...' : '写下你的想法...'}}"
          value="{{commentText}}"
          bindinput="onCommentInput"
          maxlength="500"
          auto-height>
        </textarea>

        <!-- 表情工具栏 -->
        <view class="emoji-toolbar">
          <view class="emoji-toggle" bindtap="showEmojiPanel">
            <text class="emoji-icon">😀</text>
            <text class="emoji-text">表情</text>
          </view>
          <view class="input-counter">{{commentText.length}}/500</view>
        </view>

        <!-- 表情面板 -->
        <view class="emoji-panel" wx:if="{{showEmojiPanel}}">
          <view class="emoji-grid">
            <view
              class="emoji-item"
              wx:for="{{emojiList}}"
              wx:key="*this"
              data-emoji="{{item}}"
              bindtap="insertEmoji"
            >{{item}}</view>
          </view>
        </view>
      </view>

      <view class="modal-footer">
        <button class="cancel-button" bindtap="hideCommentModal">取消</button>
        <button
          class="submit-button {{commentText.trim() ? 'active' : ''}}"
          bindtap="submitComment"
          disabled="{{!commentText.trim() || isSubmittingComment}}">
          {{isSubmittingComment ? '发布中...' : '发布'}}
        </button>
      </view>
    </view>
  </view>

  <!-- 分享弹窗 -->
  <view class="modal-overlay" wx:if="{{showShareModal}}" bindtap="hideShareModal">
    <view class="share-modal" catchtap="">
      <view class="modal-header">
        <text class="modal-title">分享笔记</text>
        <text class="modal-close" bindtap="hideShareModal">✕</text>
      </view>

      <!-- 二维码显示 -->
      <view class="qrcode-section" wx:if="{{showQRCode}}">
        <view class="qrcode-container">
          <image class="qrcode-image" src="{{qrCodeUrl}}" mode="aspectFit"></image>
          <text class="qrcode-hint">扫描二维码分享笔记</text>
          <button class="save-qrcode-btn" bindtap="saveQRCode">保存到相册</button>
        </view>
      </view>

      <!-- 分享选项 -->
      <view class="share-options" wx:else>
        <view
          class="share-option"
          wx:for="{{shareOptions}}"
          wx:key="type"
          data-type="{{item.type}}"
          bindtap="handleShareOption"
        >
          <text class="share-icon">{{item.icon}}</text>
          <text class="share-text">{{item.name}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 收藏夹选择弹窗 -->
  <view class="modal-overlay" wx:if="{{showCollectionModal}}" bindtap="hideCollectionModal">
    <view class="collection-modal" catchtap="">
      <view class="modal-header">
        <text class="modal-title">选择收藏夹</text>
        <text class="modal-close" bindtap="hideCollectionModal">✕</text>
      </view>

      <!-- 创建收藏夹 -->
      <view class="create-collection" wx:if="{{showCreateCollection}}">
        <input
          class="collection-name-input"
          placeholder="输入收藏夹名称"
          value="{{newCollectionName}}"
          bindinput="onCollectionNameInput"
          maxlength="20"
        />
        <view class="create-actions">
          <button class="cancel-create-btn" bindtap="hideCreateCollection">取消</button>
          <button class="confirm-create-btn" bindtap="createCollection">创建</button>
        </view>
      </view>

      <!-- 收藏夹列表 -->
      <view class="collection-list" wx:else>
        <view
          class="collection-item {{selectedCollections.includes(item.id) ? 'selected' : ''}}"
          wx:for="{{collections}}"
          wx:key="id"
          data-id="{{item.id}}"
          bindtap="toggleCollectionSelection"
        >
          <view class="collection-info">
            <text class="collection-name">{{item.name}}</text>
            <text class="collection-count">{{item.noteCount || 0}}篇笔记</text>
          </view>
          <view class="collection-check">
            <text wx:if="{{selectedCollections.includes(item.id)}}" class="check-icon">✓</text>
          </view>
        </view>

        <view class="collection-item create-new" bindtap="showCreateCollection">
          <view class="collection-info">
            <text class="collection-name">+ 创建新收藏夹</text>
          </view>
        </view>
      </view>

      <view class="modal-footer" wx:if="{{!showCreateCollection}}">
        <button class="cancel-button" bindtap="hideCollectionModal">取消</button>
        <button
          class="submit-button {{selectedCollections.length > 0 ? 'active' : ''}}"
          bindtap="confirmCollect"
          disabled="{{selectedCollections.length === 0}}">
          收藏到{{selectedCollections.length}}个收藏夹
        </button>
      </view>
    </view>
  </view>
</view>

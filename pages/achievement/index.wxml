<!-- pages/achievement/index.wxml -->
<!-- 成就系统页面 -->

<view class="achievement-container {{isDarkMode ? 'dark-mode' : ''}}">
  
  <!-- 加载状态 -->
  <view wx:if="{{isLoading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 加载失败状态 -->
  <view wx:elif="{{loadingFailed}}" class="error-container">
    <view class="error-icon">⚠️</view>
    <text class="error-text">加载失败</text>
    <button class="retry-btn" bindtap="retryLoad">重新加载</button>
  </view>

  <!-- 成就内容 -->
  <view wx:else class="achievement-content">
    
    <!-- 统计概览卡片 -->
    <view class="stats-card">
      <view class="stats-header">
        <text class="stats-title">成就概览</text>
        <view class="completion-rate">
          <text class="rate-text">{{stats.completionRate}}%</text>
          <text class="rate-label">完成度</text>
        </view>
      </view>
      
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-value">{{stats.unlockedAchievements}}</text>
          <text class="stat-label">已获得</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{stats.totalAchievements}}</text>
          <text class="stat-label">总成就</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{stats.totalPoints}}</text>
          <text class="stat-label">总积分</text>
        </view>
      </view>
      
      <!-- 进度条 -->
      <view class="progress-bar">
        <view class="progress-fill" style="width: {{stats.completionRate}}%"></view>
      </view>
    </view>

    <!-- 筛选控制栏 -->
    <view class="filter-bar">
      <!-- 分类筛选 -->
      <view class="filter-section">
        <text class="filter-title">分类</text>
        <scroll-view class="filter-scroll" scroll-x>
          <view class="filter-options">
            <view 
              wx:for="{{categories}}" 
              wx:key="key"
              class="filter-option {{currentCategory === item.key ? 'active' : ''}}"
              data-category="{{item.key}}"
              bindtap="switchCategory"
            >
              <text class="option-icon">{{item.icon}}</text>
              <text class="option-text">{{item.name}}</text>
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 难度筛选 -->
      <view class="filter-section">
        <text class="filter-title">难度</text>
        <scroll-view class="filter-scroll" scroll-x>
          <view class="filter-options">
            <view 
              wx:for="{{difficulties}}" 
              wx:key="key"
              class="filter-option difficulty-option {{currentDifficulty === item.key ? 'active' : ''}}"
              data-difficulty="{{item.key}}"
              bindtap="switchDifficulty"
              style="border-color: {{item.color}}"
            >
              <text class="option-text" style="color: {{item.color}}">{{item.name}}</text>
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 显示选项 -->
      <view class="display-options">
        <view class="option-group">
          <text class="option-label">显示未解锁</text>
          <view class="switch-container" bindtap="toggleShowLocked">
            <view class="switch {{showLocked ? 'active' : ''}}">
              <view class="switch-thumb"></view>
            </view>
          </view>
        </view>
        
        <view class="option-group">
          <text class="option-label">显示方式</text>
          <view class="display-toggle" bindtap="switchDisplayType">
            <text class="toggle-icon">{{displayType === 'grid' ? '⊞' : '☰'}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 成就展示区域 -->
    <view class="achievements-section">
      <achievement-display
        userId="{{currentUser.id}}"
        achievements="{{filteredAchievements}}"
        badges="{{badges}}"
        type="{{displayType}}"
        showLocked="{{showLocked}}"
        bind:itemclick="handleAchievementClick"
      ></achievement-display>
    </view>

    <!-- 空状态 -->
    <view wx:if="{{filteredAchievements.length === 0 && !isLoading}}" class="empty-state">
      <view class="empty-icon">🏆</view>
      <text class="empty-title">暂无成就</text>
      <text class="empty-desc">
        {{showLocked ? '当前筛选条件下没有找到成就' : '还没有解锁任何成就，继续努力吧！'}}
      </text>
      <button wx:if="{{!showLocked}}" class="show-all-btn" bindtap="toggleShowLocked">
        查看全部成就
      </button>
    </view>

    <!-- 底部操作区 -->
    <view class="bottom-actions">
      <button class="action-btn share-btn" open-type="share">
        <text class="btn-icon">📤</text>
        <text class="btn-text">分享成就</text>
      </button>
      
      <button class="action-btn timeline-btn" open-type="shareTimeline">
        <text class="btn-icon">🌐</text>
        <text class="btn-text">分享到朋友圈</text>
      </button>
    </view>
  </view>
</view>

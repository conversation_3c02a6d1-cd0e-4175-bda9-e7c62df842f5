/* pages/achievement/index.wxss */
/* 成就系统页面样式 */

.achievement-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx;
}

/* 深色模式 */
.achievement-container.dark-mode {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: #ecf0f1;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e3e3e3;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  color: #fff;
  font-size: 28rpx;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.error-text {
  color: #fff;
  font-size: 32rpx;
  margin-bottom: 30rpx;
}

.retry-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 成就内容 */
.achievement-content {
  padding-bottom: 40rpx;
}

/* 统计概览卡片 */
.stats-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

.dark-mode .stats-card {
  background: rgba(52, 73, 94, 0.95);
  color: #ecf0f1;
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25rpx;
}

.stats-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
}

.dark-mode .stats-title {
  color: #ecf0f1;
}

.completion-rate {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15rpx 20rpx;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  border-radius: 15rpx;
}

.rate-text {
  font-size: 28rpx;
  font-weight: 700;
  color: white;
  line-height: 1;
}

.rate-label {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 4rpx;
}

.stats-grid {
  display: flex;
  justify-content: space-around;
  margin-bottom: 25rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.stat-value {
  font-size: 36rpx;
  font-weight: 700;
  color: #667eea;
}

.dark-mode .stat-value {
  color: #74b9ff;
}

.stat-label {
  font-size: 24rpx;
  color: #7f8c8d;
}

.dark-mode .stat-label {
  color: #bdc3c7;
}

/* 进度条 */
.progress-bar {
  height: 8rpx;
  background: #ecf0f1;
  border-radius: 4rpx;
  overflow: hidden;
}

.dark-mode .progress-bar {
  background: rgba(255, 255, 255, 0.2);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

/* 筛选控制栏 */
.filter-bar {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 25rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

.dark-mode .filter-bar {
  background: rgba(52, 73, 94, 0.95);
}

.filter-section {
  margin-bottom: 20rpx;
}

.filter-section:last-child {
  margin-bottom: 0;
}

.filter-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 15rpx;
  display: block;
}

.dark-mode .filter-title {
  color: #ecf0f1;
}

.filter-scroll {
  white-space: nowrap;
}

.filter-options {
  display: flex;
  gap: 15rpx;
}

.filter-option {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: rgba(0, 0, 0, 0.05);
  border: 2rpx solid transparent;
  border-radius: 25rpx;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.dark-mode .filter-option {
  background: rgba(255, 255, 255, 0.1);
}

.filter-option.active {
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: scale(1.05);
}

.filter-option:active {
  transform: scale(0.95);
}

.option-icon {
  font-size: 24rpx;
}

.option-text {
  font-size: 24rpx;
  font-weight: 500;
}

.difficulty-option {
  border-width: 2rpx;
  border-style: solid;
}

/* 显示选项 */
.display-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
}

.dark-mode .display-options {
  border-top-color: rgba(255, 255, 255, 0.2);
}

.option-group {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.option-label {
  font-size: 26rpx;
  color: #2c3e50;
  font-weight: 500;
}

.dark-mode .option-label {
  color: #ecf0f1;
}

/* 开关样式 */
.switch-container {
  padding: 5rpx;
}

.switch {
  width: 60rpx;
  height: 32rpx;
  background: #bdc3c7;
  border-radius: 16rpx;
  position: relative;
  transition: all 0.3s ease;
}

.switch.active {
  background: #667eea;
}

.switch-thumb {
  width: 24rpx;
  height: 24rpx;
  background: white;
  border-radius: 50%;
  position: absolute;
  top: 4rpx;
  left: 4rpx;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.switch.active .switch-thumb {
  transform: translateX(28rpx);
}

/* 显示切换按钮 */
.display-toggle {
  width: 50rpx;
  height: 50rpx;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.dark-mode .display-toggle {
  background: rgba(255, 255, 255, 0.1);
}

.display-toggle:active {
  transform: scale(0.9);
  background: rgba(0, 0, 0, 0.1);
}

.dark-mode .display-toggle:active {
  background: rgba(255, 255, 255, 0.2);
}

.toggle-icon {
  font-size: 28rpx;
  color: #667eea;
}

/* 成就展示区域 */
.achievements-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 25rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

.dark-mode .achievements-section {
  background: rgba(52, 73, 94, 0.95);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.dark-mode .empty-state {
  background: rgba(52, 73, 94, 0.95);
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 15rpx;
}

.dark-mode .empty-title {
  color: #ecf0f1;
}

.empty-desc {
  font-size: 26rpx;
  color: #7f8c8d;
  text-align: center;
  line-height: 1.5;
  margin-bottom: 30rpx;
}

.dark-mode .empty-desc {
  color: #bdc3c7;
}

.show-all-btn {
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: 500;
}

/* 底部操作区 */
.bottom-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 20rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 25rpx;
  border: none;
  border-radius: 15rpx;
  font-size: 26rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.98);
}

.share-btn {
  background: rgba(52, 152, 219, 0.9);
  color: white;
}

.timeline-btn {
  background: rgba(46, 204, 113, 0.9);
  color: white;
}

.btn-icon {
  font-size: 28rpx;
}

.btn-text {
  font-weight: 500;
}

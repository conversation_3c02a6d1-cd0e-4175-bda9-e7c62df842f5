// pages/insight-detail/index.js
// 观点详情页面

// 导入认证服务
const authService = require('../../utils/auth-service');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    insightId: null,
    insight: null,
    isLoading: true,
    loadingFailed: false,
    isLiked: false,
    isCollected: false,
    showShareModal: false,
    showCommentModal: false,
    commentText: '',
    comments: [],
    isSubmittingComment: false,
    
    // 用户信息
    currentUser: null,
    isOwner: false,
    
    // 相关观点
    relatedInsights: [],
    
    // 主题模式
    isDarkMode: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    const insightId = options.id;
    
    if (!insightId) {
      this.setData({
        isLoading: false,
        loadingFailed: true
      });
      
      wx.showToast({
        title: '缺少观点ID',
        icon: 'none'
      });
      
      return;
    }

    this.setData({ insightId });
    
    // 获取主题模式
    const themeMode = wx.getStorageSync('themeMode') || 'light';
    const isDarkMode = themeMode === 'dark' || 
                      (themeMode === 'system' && wx.getSystemInfoSync().theme === 'dark');
    
    this.setData({ isDarkMode });
    
    // 加载观点详情
    this.loadInsightDetail();
    
    // 加载当前用户信息
    this.loadCurrentUser();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 每次显示时刷新数据
    if (this.data.insightId) {
      this.loadInsightDetail();
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function () {
    this.loadInsightDetail().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 加载当前用户信息
   */
  async loadCurrentUser() {
    try {
      const isLoggedIn = await authService.isLoggedIn();
      if (isLoggedIn) {
        const userInfo = await authService.getCurrentUser();
        this.setData({ currentUser: userInfo });
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
    }
  },

  /**
   * 加载观点详情
   */
  async loadInsightDetail() {
    this.setData({ isLoading: true, loadingFailed: false });

    try {
      // 获取API客户端
      const app = getApp();
      const api = app.globalData.api;

      if (!api) {
        throw new Error('API客户端未初始化');
      }

      // 调用API获取观点详情
      const response = await api.insight.getInsight(this.data.insightId);

      if (response.success && response.data) {
        const insight = response.data;
        
        // 检查是否为观点创建者
        const isOwner = this.data.currentUser && 
                       this.data.currentUser.id === insight.creatorId;

        this.setData({
          insight,
          isOwner,
          isLiked: insight.isLiked || false,
          isCollected: insight.isCollected || false,
          isLoading: false
        });

        // 设置页面标题
        wx.setNavigationBarTitle({
          title: insight.title || '观点详情'
        });

        // 加载评论
        this.loadComments();
        
        // 加载相关观点
        this.loadRelatedInsights();

      } else {
        throw new Error(response.error || '获取观点详情失败');
      }
    } catch (error) {
      console.error('加载观点详情失败:', error);
      
      this.setData({
        isLoading: false,
        loadingFailed: true
      });

      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * 加载评论列表
   */
  async loadComments() {
    try {
      const app = getApp();
      const api = app.globalData.api;

      if (!api) return;

      const response = await api.insight.getComments(this.data.insightId);
      
      if (response.success && response.data) {
        this.setData({
          comments: response.data.comments || []
        });
      }
    } catch (error) {
      console.error('加载评论失败:', error);
    }
  },

  /**
   * 加载相关观点
   */
  async loadRelatedInsights() {
    try {
      const app = getApp();
      const api = app.globalData.api;

      if (!api || !this.data.insight) return;

      // 根据标签获取相关观点
      const tagId = this.data.insight.tagId;
      
      if (!tagId) return;

      const response = await api.insight.getInsights({
        tagId,
        page: 1,
        pageSize: 6
      });
      
      if (response.success && response.data) {
        // 过滤掉当前观点
        const relatedInsights = (response.data.insights || [])
          .filter(insight => insight.id !== this.data.insightId);
        
        this.setData({
          relatedInsights: relatedInsights.slice(0, 5) // 最多显示5个相关观点
        });
      }
    } catch (error) {
      console.error('加载相关观点失败:', error);
    }
  },

  /**
   * 点赞/取消点赞
   */
  async toggleLike() {
    if (!this.data.currentUser) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    const isLiked = this.data.isLiked;
    const insight = this.data.insight;

    // 乐观更新UI
    this.setData({
      isLiked: !isLiked,
      'insight.likeCount': (insight.likeCount || 0) + (isLiked ? -1 : 1)
    });

    try {
      const app = getApp();
      const api = app.globalData.api;

      if (!api) throw new Error('API客户端未初始化');

      const response = isLiked 
        ? await api.insight.unlikeInsight(this.data.insightId)
        : await api.insight.likeInsight(this.data.insightId);

      if (!response.success) {
        // 如果失败，回滚UI更新
        this.setData({
          isLiked: isLiked,
          'insight.likeCount': insight.likeCount || 0
        });
        
        throw new Error(response.error || '操作失败');
      }

      // 轻微振动反馈
      wx.vibrateShort({ type: 'light' });

    } catch (error) {
      console.error('点赞操作失败:', error);
      
      // 回滚UI更新
      this.setData({
        isLiked: isLiked,
        'insight.likeCount': insight.likeCount || 0
      });

      wx.showToast({
        title: error.message || '操作失败',
        icon: 'none'
      });
    }
  },

  /**
   * 收藏/取消收藏
   */
  async toggleCollect() {
    if (!this.data.currentUser) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    const isCollected = this.data.isCollected;

    // 乐观更新UI
    this.setData({
      isCollected: !isCollected
    });

    try {
      const app = getApp();
      const api = app.globalData.api;

      if (!api) throw new Error('API客户端未初始化');

      const response = isCollected 
        ? await api.insight.uncollectInsight(this.data.insightId)
        : await api.insight.collectInsight(this.data.insightId);

      if (!response.success) {
        // 如果失败，回滚UI更新
        this.setData({
          isCollected: isCollected
        });
        
        throw new Error(response.error || '操作失败');
      }

      wx.showToast({
        title: isCollected ? '已取消收藏' : '已收藏',
        icon: 'success'
      });

    } catch (error) {
      console.error('收藏操作失败:', error);
      
      // 回滚UI更新
      this.setData({
        isCollected: isCollected
      });

      wx.showToast({
        title: error.message || '操作失败',
        icon: 'none'
      });
    }
  },

  /**
   * 显示分享弹窗
   */
  showShareModal() {
    this.setData({
      showShareModal: true
    });
  },

  /**
   * 隐藏分享弹窗
   */
  hideShareModal() {
    this.setData({
      showShareModal: false
    });
  },

  /**
   * 分享给朋友
   */
  onShareAppMessage() {
    const insight = this.data.insight;
    return {
      title: insight ? `观点：${insight.content.substring(0, 30)}...` : '分享观点',
      path: `/pages/insight-detail/index?id=${this.data.insightId}`,
      imageUrl: insight ? insight.imageUrl : ''
    };
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    const insight = this.data.insight;
    return {
      title: insight ? `观点：${insight.content.substring(0, 30)}...` : '分享观点',
      query: `id=${this.data.insightId}`,
      imageUrl: insight ? insight.imageUrl : ''
    };
  },

  /**
   * 显示评论弹窗
   */
  showCommentModal() {
    if (!this.data.currentUser) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    this.setData({
      showCommentModal: true,
      commentText: ''
    });
  },

  /**
   * 隐藏评论弹窗
   */
  hideCommentModal() {
    this.setData({
      showCommentModal: false,
      commentText: ''
    });
  },

  /**
   * 评论输入
   */
  onCommentInput(e) {
    this.setData({
      commentText: e.detail.value
    });
  },

  /**
   * 提交评论
   */
  async submitComment() {
    const commentText = this.data.commentText.trim();

    if (!commentText) {
      wx.showToast({
        title: '请输入评论内容',
        icon: 'none'
      });
      return;
    }

    this.setData({ isSubmittingComment: true });

    try {
      const app = getApp();
      const api = app.globalData.api;

      if (!api) throw new Error('API客户端未初始化');

      const response = await api.insight.addComment(this.data.insightId, {
        content: commentText
      });

      if (response.success) {
        wx.showToast({
          title: '评论成功',
          icon: 'success'
        });

        // 隐藏弹窗
        this.hideCommentModal();

        // 重新加载评论
        this.loadComments();

        // 更新评论数量
        const insight = this.data.insight;
        this.setData({
          'insight.commentCount': (insight.commentCount || 0) + 1
        });

      } else {
        throw new Error(response.error || '评论失败');
      }
    } catch (error) {
      console.error('提交评论失败:', error);

      wx.showToast({
        title: error.message || '评论失败',
        icon: 'none'
      });
    } finally {
      this.setData({ isSubmittingComment: false });
    }
  },

  /**
   * 查看相关观点
   */
  viewRelatedInsight(e) {
    const insightId = e.currentTarget.dataset.id;
    if (insightId) {
      wx.navigateTo({
        url: `/pages/insight-detail/index?id=${insightId}`
      });
    }
  },

  /**
   * 重新加载
   */
  retryLoad() {
    this.loadInsightDetail();
  }
});

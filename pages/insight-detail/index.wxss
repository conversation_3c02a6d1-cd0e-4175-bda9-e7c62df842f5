/* pages/insight-detail/index.wxss */
/* 观点详情页面样式 */

.insight-detail-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20rpx;
}

/* 深色模式 */
.insight-detail-container.dark-mode {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: #ecf0f1;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e3e3e3;
  border-top: 4rpx solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  color: #666;
  font-size: 28rpx;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.error-text {
  color: #e74c3c;
  font-size: 32rpx;
  margin-bottom: 30rpx;
}

.retry-btn {
  background: #3498db;
  color: white;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 观点内容 */
.insight-content {
  padding-bottom: 40rpx;
}

.insight-main {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

.dark-mode .insight-main {
  background: rgba(52, 73, 94, 0.95);
  color: #ecf0f1;
}

/* 观点头部 */
.insight-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  gap: 15rpx;
}

.insight-type-tag {
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.official-badge {
  display: flex;
  align-items: center;
  background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
  color: white;
  padding: 8rpx 12rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  gap: 6rpx;
}

.official-icon {
  font-size: 20rpx;
}

/* 观点文本 */
.insight-text {
  margin-bottom: 25rpx;
}

.content-text {
  font-size: 32rpx;
  line-height: 1.6;
  color: #2c3e50;
  font-weight: 500;
}

.dark-mode .content-text {
  color: #ecf0f1;
}

/* 观点来源 */
.insight-source {
  margin-bottom: 20rpx;
  padding: 15rpx;
  background: rgba(52, 152, 219, 0.1);
  border-radius: 12rpx;
  border-left: 4rpx solid #3498db;
}

.source-label {
  font-size: 26rpx;
  color: #7f8c8d;
  font-weight: 500;
}

.source-text {
  font-size: 26rpx;
  color: #2c3e50;
}

.dark-mode .source-text {
  color: #bdc3c7;
}

/* 背景信息 */
.insight-background {
  margin-bottom: 25rpx;
  padding: 20rpx;
  background: rgba(155, 89, 182, 0.1);
  border-radius: 12rpx;
  border-left: 4rpx solid #9b59b6;
}

.background-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #9b59b6;
  margin-bottom: 10rpx;
}

.background-text {
  font-size: 26rpx;
  line-height: 1.5;
  color: #2c3e50;
}

.dark-mode .background-text {
  color: #bdc3c7;
}

/* 观点统计 */
.insight-stats {
  display: flex;
  gap: 30rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
}

.dark-mode .insight-stats {
  border-top-color: rgba(255, 255, 255, 0.2);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.stat-icon {
  font-size: 24rpx;
}

.stat-text {
  font-size: 24rpx;
  color: #7f8c8d;
  font-weight: 500;
}

/* 操作按钮区域 */
.action-bar {
  display: flex;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

.dark-mode .action-bar {
  background: rgba(52, 73, 94, 0.95);
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 15rpx 10rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.95);
  background: rgba(0, 0, 0, 0.05);
}

.dark-mode .action-btn:active {
  background: rgba(255, 255, 255, 0.1);
}

.action-icon {
  font-size: 32rpx;
}

.action-text {
  font-size: 22rpx;
  color: #7f8c8d;
  font-weight: 500;
}

.action-btn.liked .action-text,
.action-btn.collected .action-text {
  color: #e74c3c;
}

/* 评论区域 */
.comments-section,
.related-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

.dark-mode .comments-section,
.dark-mode .related-section {
  background: rgba(52, 73, 94, 0.95);
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #ecf0f1;
}

.dark-mode .section-title {
  color: #ecf0f1;
  border-bottom-color: rgba(255, 255, 255, 0.2);
}

/* 评论列表 */
.comment-item {
  display: flex;
  gap: 20rpx;
  margin-bottom: 25rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.dark-mode .comment-item {
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.comment-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.comment-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  overflow: hidden;
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  align-items: center;
  gap: 15rpx;
  margin-bottom: 8rpx;
}

.comment-user {
  font-size: 26rpx;
  font-weight: 500;
  color: #2c3e50;
}

.dark-mode .comment-user {
  color: #ecf0f1;
}

.comment-time {
  font-size: 22rpx;
  color: #95a5a6;
}

.comment-text {
  font-size: 26rpx;
  line-height: 1.5;
  color: #34495e;
}

.dark-mode .comment-text {
  color: #bdc3c7;
}

/* 相关观点 */
.related-item {
  padding: 20rpx;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 12rpx;
  margin-bottom: 15rpx;
  transition: all 0.3s ease;
}

.dark-mode .related-item {
  background: rgba(255, 255, 255, 0.05);
}

.related-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.related-item:last-child {
  margin-bottom: 0;
}

.related-content {
  font-size: 26rpx;
  line-height: 1.5;
  color: #2c3e50;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.dark-mode .related-content {
  color: #ecf0f1;
}

.related-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10rpx;
}

.related-source {
  font-size: 22rpx;
  color: #7f8c8d;
}

.related-stats {
  font-size: 22rpx;
  color: #95a5a6;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 40rpx;
}

.comment-modal,
.share-modal {
  background: white;
  border-radius: 20rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
}

.dark-mode .comment-modal,
.dark-mode .share-modal {
  background: #34495e;
  color: #ecf0f1;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(50rpx) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #ecf0f1;
}

.dark-mode .modal-header {
  border-bottom-color: rgba(255, 255, 255, 0.2);
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
}

.dark-mode .modal-title {
  color: #ecf0f1;
}

.modal-close {
  font-size: 36rpx;
  color: #95a5a6;
  padding: 10rpx;
  line-height: 1;
}

.modal-close:active {
  color: #7f8c8d;
}

/* 评论弹窗内容 */
.modal-content {
  padding: 30rpx;
}

.comment-input {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 2rpx solid #ecf0f1;
  border-radius: 12rpx;
  font-size: 28rpx;
  line-height: 1.5;
  background: #fafafa;
  resize: none;
  box-sizing: border-box;
}

.dark-mode .comment-input {
  background: #2c3e50;
  border-color: rgba(255, 255, 255, 0.2);
  color: #ecf0f1;
}

.comment-input:focus {
  border-color: #3498db;
  background: white;
}

.dark-mode .comment-input:focus {
  background: #34495e;
  border-color: #3498db;
}

.comment-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20rpx;
}

.char-count {
  font-size: 24rpx;
  color: #95a5a6;
}

.submit-btn {
  background: #bdc3c7;
  color: white;
  border: none;
  border-radius: 25rpx;
  padding: 15rpx 30rpx;
  font-size: 26rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.submit-btn.active {
  background: linear-gradient(45deg, #3498db 0%, #2980b9 100%);
}

.submit-btn.active:active {
  transform: scale(0.95);
}

/* 分享弹窗 */
.share-options {
  padding: 30rpx;
}

.share-btn {
  display: flex;
  align-items: center;
  gap: 20rpx;
  width: 100%;
  padding: 25rpx;
  margin-bottom: 15rpx;
  background: rgba(52, 152, 219, 0.1);
  border: none;
  border-radius: 15rpx;
  font-size: 28rpx;
  color: #2c3e50;
  transition: all 0.3s ease;
}

.dark-mode .share-btn {
  background: rgba(52, 152, 219, 0.2);
  color: #ecf0f1;
}

.share-btn:active {
  transform: scale(0.98);
  background: rgba(52, 152, 219, 0.2);
}

.dark-mode .share-btn:active {
  background: rgba(52, 152, 219, 0.3);
}

.share-btn:last-child {
  margin-bottom: 0;
}

.share-icon {
  font-size: 32rpx;
}

.share-text {
  font-weight: 500;
}

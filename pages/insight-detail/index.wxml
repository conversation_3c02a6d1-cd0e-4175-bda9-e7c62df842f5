<!-- pages/insight-detail/index.wxml -->
<!-- 观点详情页面 -->

<view class="insight-detail-container {{isDarkMode ? 'dark-mode' : ''}}">
  
  <!-- 加载状态 -->
  <view wx:if="{{isLoading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 加载失败状态 -->
  <view wx:elif="{{loadingFailed}}" class="error-container">
    <view class="error-icon">⚠️</view>
    <text class="error-text">加载失败</text>
    <button class="retry-btn" bindtap="retryLoad">重新加载</button>
  </view>

  <!-- 观点内容 -->
  <view wx:else class="insight-content">
    
    <!-- 观点主体内容 -->
    <view class="insight-main">
      <view class="insight-header">
        <!-- 观点类型标签 -->
        <view wx:if="{{insight.type}}" class="insight-type-tag">
          {{insight.type === 'quote' ? '名言' : insight.type === 'concept' ? '概念' : insight.type === 'principle' ? '原理' : insight.type === 'fact' ? '事实' : '问题'}}
        </view>
        
        <!-- 官方标识 -->
        <view wx:if="{{insight.isOfficial}}" class="official-badge">
          <text class="official-icon">✓</text>
          <text class="official-text">官方</text>
        </view>
      </view>

      <!-- 观点内容 -->
      <view class="insight-text">
        <text class="content-text">{{insight.content}}</text>
      </view>

      <!-- 观点来源 -->
      <view wx:if="{{insight.source}}" class="insight-source">
        <text class="source-label">来源：</text>
        <text class="source-text">{{insight.source}}</text>
      </view>

      <!-- 背景信息 -->
      <view wx:if="{{insight.background}}" class="insight-background">
        <view class="background-title">背景信息</view>
        <text class="background-text">{{insight.background}}</text>
      </view>

      <!-- 观点统计 -->
      <view class="insight-stats">
        <view class="stat-item">
          <text class="stat-icon">👁</text>
          <text class="stat-text">{{insight.viewCount || 0}}</text>
        </view>
        <view class="stat-item">
          <text class="stat-icon">👍</text>
          <text class="stat-text">{{insight.likeCount || 0}}</text>
        </view>
        <view class="stat-item">
          <text class="stat-icon">💬</text>
          <text class="stat-text">{{insight.commentCount || 0}}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮区域 -->
    <view class="action-bar">
      <view class="action-btn {{isLiked ? 'liked' : ''}}" bindtap="toggleLike">
        <text class="action-icon">{{isLiked ? '❤️' : '🤍'}}</text>
        <text class="action-text">{{isLiked ? '已赞' : '点赞'}}</text>
      </view>
      
      <view class="action-btn {{isCollected ? 'collected' : ''}}" bindtap="toggleCollect">
        <text class="action-icon">{{isCollected ? '⭐' : '☆'}}</text>
        <text class="action-text">{{isCollected ? '已收藏' : '收藏'}}</text>
      </view>
      
      <view class="action-btn" bindtap="showCommentModal">
        <text class="action-icon">💬</text>
        <text class="action-text">评论</text>
      </view>
      
      <view class="action-btn" bindtap="showShareModal">
        <text class="action-icon">📤</text>
        <text class="action-text">分享</text>
      </view>
    </view>

    <!-- 评论区域 -->
    <view wx:if="{{comments.length > 0}}" class="comments-section">
      <view class="section-title">评论 ({{comments.length}})</view>
      <view class="comments-list">
        <view wx:for="{{comments}}" wx:key="id" class="comment-item">
          <view class="comment-avatar">
            <image src="{{item.userAvatar || '/assets/icons/default-avatar.png'}}" class="avatar-img" />
          </view>
          <view class="comment-content">
            <view class="comment-header">
              <text class="comment-user">{{item.userName || '匿名用户'}}</text>
              <text class="comment-time">{{item.createdAt}}</text>
            </view>
            <text class="comment-text">{{item.content}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 相关观点推荐 -->
    <view wx:if="{{relatedInsights.length > 0}}" class="related-section">
      <view class="section-title">相关观点</view>
      <view class="related-list">
        <view 
          wx:for="{{relatedInsights}}" 
          wx:key="id" 
          class="related-item"
          data-id="{{item.id}}"
          bindtap="viewRelatedInsight"
        >
          <text class="related-content">{{item.content}}</text>
          <view class="related-meta">
            <text class="related-source" wx:if="{{item.source}}">{{item.source}}</text>
            <text class="related-stats">👍 {{item.likeCount || 0}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 评论弹窗 -->
  <view wx:if="{{showCommentModal}}" class="modal-overlay" bindtap="hideCommentModal">
    <view class="comment-modal" catchtap="">
      <view class="modal-header">
        <text class="modal-title">添加评论</text>
        <text class="modal-close" bindtap="hideCommentModal">✕</text>
      </view>
      <view class="modal-content">
        <textarea 
          class="comment-input"
          placeholder="说说你的看法..."
          value="{{commentText}}"
          bindinput="onCommentInput"
          maxlength="500"
          auto-height
        ></textarea>
        <view class="comment-actions">
          <text class="char-count">{{commentText.length}}/500</text>
          <button 
            class="submit-btn {{commentText.trim() ? 'active' : ''}}"
            disabled="{{!commentText.trim() || isSubmittingComment}}"
            bindtap="submitComment"
          >
            {{isSubmittingComment ? '发布中...' : '发布'}}
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 分享弹窗 -->
  <view wx:if="{{showShareModal}}" class="modal-overlay" bindtap="hideShareModal">
    <view class="share-modal" catchtap="">
      <view class="modal-header">
        <text class="modal-title">分享观点</text>
        <text class="modal-close" bindtap="hideShareModal">✕</text>
      </view>
      <view class="share-options">
        <button class="share-btn" open-type="share">
          <text class="share-icon">👥</text>
          <text class="share-text">分享给朋友</text>
        </button>
        <button class="share-btn" open-type="shareTimeline">
          <text class="share-icon">🌐</text>
          <text class="share-text">分享到朋友圈</text>
        </button>
      </view>
    </view>
  </view>
</view>

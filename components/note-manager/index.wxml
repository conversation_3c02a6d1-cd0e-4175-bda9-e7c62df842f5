<!-- components/note-manager/index.wxml -->
<view class="note-manager-container">
  <!-- 标题栏 -->
  <view class="header">
    <view class="title">我的笔记</view>
    <view class="actions">
      <view wx:if="{{!isSelectMode}}" class="action-btn select-mode-btn" bindtap="toggleSelectMode">
        <text class="icon">☑</text>
        <text>批量操作</text>
      </view>
      <view wx:else class="action-btn exit-select-btn" bindtap="toggleSelectMode">
        <text class="icon">✕</text>
        <text>退出选择</text>
      </view>
      <view class="action-btn refresh-btn" bindtap="refreshList">
        <text class="icon">↻</text>
        <text>刷新</text>
      </view>
      <view class="action-btn create-btn" bindtap="createNote">
        <text class="icon">+</text>
        <text>新建</text>
      </view>
    </view>
  </view>

  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input-wrapper">
      <text class="search-icon">🔍</text>
      <input
        class="search-input"
        placeholder="搜索笔记"
        value="{{searchKeyword}}"
        confirm-type="search"
        bindconfirm="handleSearch"
      />
      <text wx:if="{{searchKeyword}}" class="clear-icon" bindtap="clearSearch">✕</text>
    </view>

    <!-- 搜索工具栏 -->
    <view class="search-tools">
      <button class="tool-btn filter-btn" bindtap="toggleFilterPanel">
        <text class="tool-icon">🔽</text>
        <text class="tool-text">筛选</text>
        <text wx:if="{{filters.category || filters.tag || filters.isPublic || filters.dateRange}}" class="filter-dot"></text>
      </button>

      <button class="tool-btn sort-btn" bindtap="toggleSortPanel">
        <text class="tool-icon">📊</text>
        <text class="tool-text">排序</text>
      </button>

      <button class="tool-btn advanced-btn" bindtap="toggleAdvancedSearch">
        <text class="tool-icon">🔍</text>
        <text class="tool-text">高级</text>
      </button>

      <button class="tool-btn view-btn" data-mode="{{viewMode === 'list' ? 'grid' : 'list'}}" bindtap="switchViewMode">
        <text class="tool-icon">{{viewMode === 'list' ? '⊞' : '☰'}}</text>
        <text class="tool-text">{{viewMode === 'list' ? '网格' : '列表'}}</text>
      </button>
    </view>
  </view>

  <!-- 筛选面板 -->
  <view class="filter-panel" wx:if="{{showFilterPanel}}">
    <view class="panel-header">
      <text class="panel-title">筛选条件</text>
      <button class="clear-btn" bindtap="clearFilters">清除</button>
    </view>

    <view class="filter-section">
      <text class="filter-label">分类</text>
      <scroll-view class="filter-options" scroll-x>
        <view
          class="filter-option {{filters.category === '' ? 'active' : ''}}"
          data-field="category"
          data-value=""
          bindtap="setFilter"
        >全部</view>
        <view
          class="filter-option {{filters.category === item.id ? 'active' : ''}}"
          wx:for="{{categories}}"
          wx:key="id"
          data-field="category"
          data-value="{{item.id}}"
          bindtap="setFilter"
        >{{item.name}}</view>
      </scroll-view>
    </view>

    <view class="filter-section">
      <text class="filter-label">标签</text>
      <scroll-view class="filter-options" scroll-x>
        <view
          class="filter-option {{filters.tag === '' ? 'active' : ''}}"
          data-field="tag"
          data-value=""
          bindtap="setFilter"
        >全部</view>
        <view
          class="filter-option {{filters.tag === item.id ? 'active' : ''}}"
          wx:for="{{tags}}"
          wx:key="id"
          data-field="tag"
          data-value="{{item.id}}"
          bindtap="setFilter"
        >{{item.name}}</view>
      </scroll-view>
    </view>

    <view class="filter-section">
      <text class="filter-label">状态</text>
      <view class="filter-options">
        <view
          class="filter-option {{filters.isPublic === '' ? 'active' : ''}}"
          data-field="isPublic"
          data-value=""
          bindtap="setFilter"
        >全部</view>
        <view
          class="filter-option {{filters.isPublic === 'true' ? 'active' : ''}}"
          data-field="isPublic"
          data-value="true"
          bindtap="setFilter"
        >公开</view>
        <view
          class="filter-option {{filters.isPublic === 'false' ? 'active' : ''}}"
          data-field="isPublic"
          data-value="false"
          bindtap="setFilter"
        >私密</view>
      </view>
    </view>

    <view class="filter-section">
      <text class="filter-label">时间</text>
      <view class="filter-options">
        <view
          class="filter-option {{filters.dateRange === '' ? 'active' : ''}}"
          data-field="dateRange"
          data-value=""
          bindtap="setFilter"
        >全部</view>
        <view
          class="filter-option {{filters.dateRange === 'today' ? 'active' : ''}}"
          data-field="dateRange"
          data-value="today"
          bindtap="setFilter"
        >今天</view>
        <view
          class="filter-option {{filters.dateRange === 'week' ? 'active' : ''}}"
          data-field="dateRange"
          data-value="week"
          bindtap="setFilter"
        >本周</view>
        <view
          class="filter-option {{filters.dateRange === 'month' ? 'active' : ''}}"
          data-field="dateRange"
          data-value="month"
          bindtap="setFilter"
        >本月</view>
      </view>
    </view>
  </view>

  <!-- 排序面板 -->
  <view class="sort-panel" wx:if="{{showSortPanel}}">
    <view class="panel-header">
      <text class="panel-title">排序方式</text>
    </view>

    <view class="sort-options">
      <view
        class="sort-option {{filters.sortBy === 'updatedAt' ? 'active' : ''}}"
        data-sort-by="updatedAt"
        bindtap="setSort"
      >
        <text class="sort-text">更新时间</text>
        <text class="sort-order" wx:if="{{filters.sortBy === 'updatedAt'}}">
          {{filters.sortOrder === 'desc' ? '↓' : '↑'}}
        </text>
      </view>

      <view
        class="sort-option {{filters.sortBy === 'createdAt' ? 'active' : ''}}"
        data-sort-by="createdAt"
        bindtap="setSort"
      >
        <text class="sort-text">创建时间</text>
        <text class="sort-order" wx:if="{{filters.sortBy === 'createdAt'}}">
          {{filters.sortOrder === 'desc' ? '↓' : '↑'}}
        </text>
      </view>

      <view
        class="sort-option {{filters.sortBy === 'title' ? 'active' : ''}}"
        data-sort-by="title"
        bindtap="setSort"
      >
        <text class="sort-text">标题</text>
        <text class="sort-order" wx:if="{{filters.sortBy === 'title'}}">
          {{filters.sortOrder === 'desc' ? '↓' : '↑'}}
        </text>
      </view>

      <view
        class="sort-option {{filters.sortBy === 'viewCount' ? 'active' : ''}}"
        data-sort-by="viewCount"
        bindtap="setSort"
      >
        <text class="sort-text">浏览量</text>
        <text class="sort-order" wx:if="{{filters.sortBy === 'viewCount'}}">
          {{filters.sortOrder === 'desc' ? '↓' : '↑'}}
        </text>
      </view>
    </view>

    <view class="sort-direction">
      <button
        class="direction-btn {{filters.sortOrder === 'desc' ? 'active' : ''}}"
        data-sort-order="desc"
        bindtap="setSort"
      >降序</button>
      <button
        class="direction-btn {{filters.sortOrder === 'asc' ? 'active' : ''}}"
        data-sort-order="asc"
        bindtap="setSort"
      >升序</button>
    </view>
  </view>

  <!-- 高级搜索面板 -->
  <view class="advanced-search-panel" wx:if="{{showAdvancedSearch}}">
    <view class="panel-header">
      <text class="panel-title">高级搜索</text>
      <button class="clear-btn" bindtap="clearAdvancedSearch">清除</button>
    </view>

    <view class="search-fields">
      <view class="search-field">
        <text class="field-label">标题</text>
        <input
          class="field-input"
          placeholder="搜索标题"
          value="{{advancedSearch.title}}"
          data-field="title"
          bindinput="handleAdvancedSearchInput"
        />
      </view>

      <view class="search-field">
        <text class="field-label">内容</text>
        <input
          class="field-input"
          placeholder="搜索内容"
          value="{{advancedSearch.content}}"
          data-field="content"
          bindinput="handleAdvancedSearchInput"
        />
      </view>

      <view class="search-field">
        <text class="field-label">作者</text>
        <input
          class="field-input"
          placeholder="搜索作者"
          value="{{advancedSearch.author}}"
          data-field="author"
          bindinput="handleAdvancedSearchInput"
        />
      </view>

      <view class="search-field">
        <text class="field-label">日期范围</text>
        <view class="date-range">
          <picker
            mode="date"
            value="{{advancedSearch.dateFrom}}"
            data-field="dateFrom"
            bindchange="handleAdvancedSearchInput"
          >
            <input
              class="date-input"
              placeholder="开始日期"
              value="{{advancedSearch.dateFrom}}"
              disabled
            />
          </picker>
          <text class="date-separator">至</text>
          <picker
            mode="date"
            value="{{advancedSearch.dateTo}}"
            data-field="dateTo"
            bindchange="handleAdvancedSearchInput"
          >
            <input
              class="date-input"
              placeholder="结束日期"
              value="{{advancedSearch.dateTo}}"
              disabled
            />
          </picker>
        </view>
      </view>
    </view>

    <view class="search-actions">
      <button class="search-btn" bindtap="performAdvancedSearch">搜索</button>
    </view>
  </view>

  <!-- 标签页 -->
  <view class="tabs">
    <view 
      class="tab {{activeTab === 'all' ? 'active' : ''}}" 
      data-tab="all" 
      bindtap="switchTab"
    >全部笔记</view>
    <view 
      wx:if="{{showRecycleBin}}"
      class="tab {{activeTab === 'deleted' ? 'active' : ''}}" 
      data-tab="deleted" 
      bindtap="switchTab"
    >回收站</view>
  </view>

  <!-- 统计信息栏 -->
  <view class="statistics-bar" wx:if="{{!isSelectMode && statistics.totalNotes > 0}}">
    <view class="stat-item">
      <text class="stat-number">{{statistics.totalNotes}}</text>
      <text class="stat-label">总计</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{statistics.publicNotes}}</text>
      <text class="stat-label">公开</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{statistics.privateNotes}}</text>
      <text class="stat-label">私密</text>
    </view>
    <view class="stat-item" wx:if="{{statistics.deletedNotes > 0}}">
      <text class="stat-number">{{statistics.deletedNotes}}</text>
      <text class="stat-label">已删除</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{statistics.totalWords}}</text>
      <text class="stat-label">总字数</text>
    </view>
  </view>

  <!-- 批量操作工具栏 -->
  <view class="batch-toolbar" wx:if="{{isSelectMode}}">
    <view class="selection-info">
      <text class="selection-count">已选择 {{selectedNotes.length}} 篇</text>
      <text class="selection-hint" wx:if="{{selectedNotes.length > 0}}">
        {{activeTab === 'all' ? '可删除或导出' : '可恢复'}}
      </text>
    </view>

    <view class="batch-actions">
      <button class="batch-btn select-all-btn" bindtap="toggleSelectAll">
        <text class="btn-icon">{{selectedNotes.length === notes.length ? '☐' : '☑'}}</text>
        <text class="btn-text">{{selectedNotes.length === notes.length ? '取消全选' : '全选'}}</text>
      </button>

      <button
        wx:if="{{activeTab === 'all'}}"
        class="batch-btn export-btn"
        bindtap="exportNotes"
        disabled="{{selectedNotes.length === 0}}"
      >
        <text class="btn-icon">📤</text>
        <text class="btn-text">导出</text>
      </button>

      <button
        wx:if="{{activeTab === 'all'}}"
        class="batch-btn delete-btn"
        bindtap="batchSoftDelete"
        disabled="{{selectedNotes.length === 0}}"
      >
        <text class="btn-icon">🗑</text>
        <text class="btn-text">删除</text>
      </button>

      <button
        wx:else
        class="batch-btn restore-btn"
        bindtap="batchRestore"
        disabled="{{selectedNotes.length === 0}}"
      >
        <text class="btn-icon">↩</text>
        <text class="btn-text">恢复</text>
      </button>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-container" wx:elif="{{notes.length === 0}}">
    <view class="empty-icon">📝</view>
    <text class="empty-text">{{activeTab === 'deleted' ? '回收站为空' : '暂无笔记'}}</text>
    <view wx:if="{{activeTab === 'all'}}" class="empty-action" bindtap="createNote">创建笔记</view>
  </view>

  <!-- 笔记列表 -->
  <view class="notes-container {{viewMode}}-view" wx:else>
    <!-- 列表视图 -->
    <view
      wx:if="{{viewMode === 'list'}}"
      class="note-item list-item {{isSelectMode ? 'selectable' : ''}} {{selectedNotes.includes(item.id) ? 'selected' : ''}}"
      wx:for="{{notes}}"
      wx:key="id"
      data-id="{{item.id}}"
      bindtap="{{isSelectMode ? 'toggleSelectNote' : 'viewNoteDetail'}}"
    >
      <!-- 选择框 -->
      <view class="select-box" wx:if="{{isSelectMode}}">
        <view class="checkbox {{selectedNotes.includes(item.id) ? 'checked' : ''}}">
          <text wx:if="{{selectedNotes.includes(item.id)}}" class="check-icon">✓</text>
        </view>
      </view>

      <!-- 笔记信息 -->
      <view class="note-info">
        <view class="note-header">
          <view class="note-title">{{item.title}}</view>
          <view class="note-badges">
            <text class="badge public-badge" wx:if="{{item.isPublic}}">公开</text>
            <text class="badge private-badge" wx:else>私密</text>
            <text class="badge category-badge" wx:if="{{item.category}}">{{item.category.name}}</text>
          </view>
        </view>

        <view class="note-content">{{item.content}}</view>

        <view class="note-meta">
          <view class="meta-left">
            <text class="note-time">{{activeTab === 'deleted' ? '删除时间: ' + item.deletedAt : '更新时间: ' + item.updatedAt}}</text>
            <text class="note-stats" wx:if="{{item.viewCount}}">浏览 {{item.viewCount}}</text>
            <text class="note-stats" wx:if="{{item.wordCount}}">{{item.wordCount}} 字</text>
          </view>

          <view class="note-tags" wx:if="{{item.tags && item.tags.length > 0}}">
            <text class="tag" wx:for="{{item.tags}}" wx:key="id" wx:for-item="tag">#{{tag.name}}</text>
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="note-actions" wx:if="{{!isSelectMode}}" catchtap>
        <button wx:if="{{activeTab === 'all'}}" class="action-btn edit-btn" data-id="{{item.id}}" catchtap="editNote">
          <text class="btn-icon">✏</text>
          <text class="btn-text">编辑</text>
        </button>
        <button wx:if="{{activeTab === 'all'}}" class="action-btn delete-btn" data-id="{{item.id}}" catchtap="softDeleteNote">
          <text class="btn-icon">🗑</text>
          <text class="btn-text">删除</text>
        </button>
        <button wx:else class="action-btn restore-btn" data-id="{{item.id}}" catchtap="restoreNote">
          <text class="btn-icon">↩</text>
          <text class="btn-text">恢复</text>
        </button>
      </view>
    </view>

    <!-- 网格视图 -->
    <view
      wx:if="{{viewMode === 'grid'}}"
      class="note-item grid-item {{isSelectMode ? 'selectable' : ''}} {{selectedNotes.includes(item.id) ? 'selected' : ''}}"
      wx:for="{{notes}}"
      wx:key="id"
      data-id="{{item.id}}"
      bindtap="{{isSelectMode ? 'toggleSelectNote' : 'viewNoteDetail'}}"
    >
      <!-- 选择框 -->
      <view class="select-box" wx:if="{{isSelectMode}}">
        <view class="checkbox {{selectedNotes.includes(item.id) ? 'checked' : ''}}">
          <text wx:if="{{selectedNotes.includes(item.id)}}" class="check-icon">✓</text>
        </view>
      </view>

      <!-- 笔记卡片 -->
      <view class="note-card">
        <view class="card-header">
          <view class="note-title">{{item.title}}</view>
          <view class="note-status">
            <text class="status-dot {{item.isPublic ? 'public' : 'private'}}"></text>
          </view>
        </view>

        <view class="card-content">
          <text class="note-preview">{{item.content}}</text>
        </view>

        <view class="card-footer">
          <view class="note-meta">
            <text class="note-time">{{item.updatedAt}}</text>
            <text class="note-stats" wx:if="{{item.wordCount}}">{{item.wordCount}}字</text>
          </view>

          <view class="card-actions" wx:if="{{!isSelectMode}}" catchtap>
            <button wx:if="{{activeTab === 'all'}}" class="card-action-btn" data-id="{{item.id}}" catchtap="editNote">
              <text class="action-icon">✏</text>
            </button>
            <button wx:if="{{activeTab === 'all'}}" class="card-action-btn" data-id="{{item.id}}" catchtap="softDeleteNote">
              <text class="action-icon">🗑</text>
            </button>
            <button wx:else class="card-action-btn" data-id="{{item.id}}" catchtap="restoreNote">
              <text class="action-icon">↩</text>
            </button>
          </view>
        </view>

        <!-- 标签 -->
        <view class="card-tags" wx:if="{{item.tags && item.tags.length > 0}}">
          <text class="card-tag" wx:for="{{item.tags}}" wx:key="id" wx:for-item="tag">{{tag.name}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 分页控制 -->
  <view class="pagination" wx:if="{{notes.length > 0}}">
    <view class="page-btn prev {{pagination.page <= 1 ? 'disabled' : ''}}" bindtap="handlePrevPage">上一页</view>
    <view class="page-info">{{pagination.page}} / {{Math.ceil(pagination.total / pagination.pageSize)}}</view>
    <view class="page-btn next {{pagination.page * pagination.pageSize >= pagination.total ? 'disabled' : ''}}" bindtap="handleNextPage">下一页</view>
  </view>
</view>

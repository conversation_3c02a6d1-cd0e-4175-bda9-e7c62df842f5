/* components/note-manager/index.wxss */

/* 搜索工具栏 */
.search-tools {
  display: flex;
  gap: 16rpx;
  padding: 20rpx 30rpx;
  background: #f8fafc;
  border-radius: 16rpx;
  margin: 16rpx 30rpx;
}

.tool-btn {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 20rpx;
  background: #ffffff;
  border: 2rpx solid #e2e8f0;
  border-radius: 12rpx;
  font-size: 24rpx;
  transition: all 0.2s ease;
  flex: 1;
}

.tool-btn:active {
  background: #f1f5f9;
  transform: scale(0.95);
}

.tool-icon {
  font-size: 28rpx;
}

.tool-text {
  color: #64748b;
  font-size: 22rpx;
}

.filter-dot {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 12rpx;
  height: 12rpx;
  background: #ef4444;
  border-radius: 50%;
}

/* 筛选面板 */
.filter-panel, .sort-panel, .advanced-search-panel {
  background: #ffffff;
  border-radius: 16rpx;
  margin: 20rpx 30rpx;
  border: 2rpx solid #e2e8f0;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  border-bottom: 2rpx solid #e2e8f0;
}

.panel-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1e293b;
}

.clear-btn {
  padding: 12rpx 24rpx;
  background: #f1f5f9;
  color: #64748b;
  border: 2rpx solid #e2e8f0;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.filter-section {
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f1f5f9;
}

.filter-section:last-child {
  border-bottom: none;
}

.filter-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
  margin-bottom: 16rpx;
}

.filter-options {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}

.filter-options.scroll-x {
  white-space: nowrap;
  overflow-x: auto;
}

.filter-option {
  padding: 12rpx 24rpx;
  background: #f8fafc;
  color: #64748b;
  border: 2rpx solid #e2e8f0;
  border-radius: 20rpx;
  font-size: 24rpx;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.filter-option.active {
  background: #3b82f6;
  color: #ffffff;
  border-color: #3b82f6;
}

/* 排序面板 */
.sort-options {
  padding: 24rpx 30rpx;
}

.sort-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 24rpx;
  margin-bottom: 16rpx;
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 12rpx;
  transition: all 0.2s ease;
}

.sort-option.active {
  background: #eff6ff;
  border-color: #3b82f6;
}

.sort-text {
  font-size: 28rpx;
  color: #374151;
}

.sort-order {
  font-size: 32rpx;
  color: #3b82f6;
  font-weight: bold;
}

.sort-direction {
  display: flex;
  gap: 16rpx;
  padding: 24rpx 30rpx;
  border-top: 2rpx solid #e2e8f0;
}

.direction-btn {
  flex: 1;
  padding: 16rpx;
  background: #f8fafc;
  color: #64748b;
  border: 2rpx solid #e2e8f0;
  border-radius: 8rpx;
  font-size: 26rpx;
  text-align: center;
}

.direction-btn.active {
  background: #3b82f6;
  color: #ffffff;
  border-color: #3b82f6;
}

/* 高级搜索面板 */
.search-fields {
  padding: 24rpx 30rpx;
}

.search-field {
  margin-bottom: 24rpx;
}

.field-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
  margin-bottom: 12rpx;
}

.field-input {
  width: 100%;
  padding: 20rpx 24rpx;
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #1e293b;
}

.field-input:focus {
  border-color: #3b82f6;
  background: #ffffff;
}

.date-range {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.date-input {
  flex: 1;
  padding: 20rpx 24rpx;
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #1e293b;
}

.date-separator {
  font-size: 24rpx;
  color: #64748b;
}

.search-actions {
  padding: 24rpx 30rpx;
  border-top: 2rpx solid #e2e8f0;
}

.search-btn {
  width: 100%;
  padding: 24rpx;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: #ffffff;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
}

/* 统计信息栏 */
.statistics-bar {
  display: flex;
  gap: 20rpx;
  padding: 20rpx 30rpx;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 16rpx;
  margin: 20rpx 30rpx;
  border: 2rpx solid #bae6fd;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-number {
  font-size: 32rpx;
  font-weight: bold;
  color: #0369a1;
}

.stat-label {
  font-size: 22rpx;
  color: #0284c7;
  margin-top: 4rpx;
}

/* 批量操作工具栏增强 */
.batch-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border-radius: 16rpx;
  margin: 20rpx 30rpx;
  border: 2rpx solid #fbbf24;
}

.selection-info {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.selection-count {
  font-size: 28rpx;
  font-weight: 600;
  color: #92400e;
}

.selection-hint {
  font-size: 22rpx;
  color: #d97706;
}

.batch-actions {
  display: flex;
  gap: 12rpx;
}

.batch-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
  padding: 16rpx 20rpx;
  background: #ffffff;
  border: 2rpx solid #f59e0b;
  border-radius: 12rpx;
  font-size: 22rpx;
  transition: all 0.2s ease;
  min-width: 100rpx;
}

.batch-btn:disabled {
  opacity: 0.5;
  background: #f3f4f6;
  border-color: #d1d5db;
}

.batch-btn:not(:disabled):active {
  transform: scale(0.95);
}

.batch-btn.export-btn {
  background: #dbeafe;
  border-color: #3b82f6;
}

.batch-btn.delete-btn {
  background: #fee2e2;
  border-color: #ef4444;
}

.batch-btn.restore-btn {
  background: #dcfce7;
  border-color: #22c55e;
}

.btn-icon {
  font-size: 24rpx;
}

.btn-text {
  color: #374151;
  font-size: 20rpx;
}

/* 笔记列表视图增强 */
.notes-container.list-view {
  padding: 0 30rpx;
}

.note-item.list-item {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  background: #ffffff;
  border-radius: 16rpx;
  border: 2rpx solid #e2e8f0;
  transition: all 0.2s ease;
}

.note-item.list-item:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.note-item.list-item.selected {
  border-color: #3b82f6;
  background: #eff6ff;
}

.note-info {
  flex: 1;
  min-width: 0;
}

.note-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12rpx;
}

.note-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1e293b;
  line-height: 1.4;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.note-badges {
  display: flex;
  gap: 8rpx;
  flex-shrink: 0;
  margin-left: 16rpx;
}

.badge {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.badge.public-badge {
  background: #dcfce7;
  color: #166534;
}

.badge.private-badge {
  background: #fef3c7;
  color: #92400e;
}

.badge.category-badge {
  background: #e0e7ff;
  color: #3730a3;
}

.note-content {
  font-size: 28rpx;
  color: #64748b;
  line-height: 1.5;
  margin-bottom: 16rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.note-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.meta-left {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.note-time {
  font-size: 24rpx;
  color: #94a3b8;
}

.note-stats {
  font-size: 22rpx;
  color: #94a3b8;
}

.note-tags {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
}

.tag {
  font-size: 20rpx;
  color: #6366f1;
  background: #f0f9ff;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.note-actions {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  flex-shrink: 0;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 8rpx;
  font-size: 24rpx;
  transition: all 0.2s ease;
  min-width: 120rpx;
}

.action-btn.edit-btn {
  background: #eff6ff;
  border-color: #3b82f6;
}

.action-btn.delete-btn {
  background: #fef2f2;
  border-color: #ef4444;
}

.action-btn.restore-btn {
  background: #f0fdf4;
  border-color: #22c55e;
}

.action-btn:active {
  transform: scale(0.95);
}

/* 网格视图 */
.notes-container.grid-view {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  padding: 0 30rpx;
}

.note-item.grid-item {
  background: #ffffff;
  border-radius: 16rpx;
  border: 2rpx solid #e2e8f0;
  overflow: hidden;
  transition: all 0.2s ease;
}

.note-item.grid-item:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.note-item.grid-item.selected {
  border-color: #3b82f6;
  background: #eff6ff;
}

.note-card {
  padding: 20rpx;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.card-header .note-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1e293b;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  flex: 1;
}

.note-status {
  flex-shrink: 0;
  margin-left: 12rpx;
}

.status-dot {
  display: inline-block;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
}

.status-dot.public {
  background: #22c55e;
}

.status-dot.private {
  background: #f59e0b;
}

.card-content {
  flex: 1;
  margin-bottom: 16rpx;
}

.note-preview {
  font-size: 24rpx;
  color: #64748b;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.card-footer .note-meta {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.card-footer .note-time {
  font-size: 20rpx;
  color: #94a3b8;
}

.card-footer .note-stats {
  font-size: 18rpx;
  color: #94a3b8;
}

.card-actions {
  display: flex;
  gap: 8rpx;
}

.card-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 8rpx;
  font-size: 20rpx;
  transition: all 0.2s ease;
}

.card-action-btn:active {
  transform: scale(0.9);
}

.card-tags {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
  margin-top: 12rpx;
}

.card-tag {
  font-size: 18rpx;
  color: #6366f1;
  background: #f0f9ff;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  border: 1rpx solid #c7d2fe;
}
.note-manager-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f8f8f8;
}

/* 标题栏样式 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #eeeeee;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #666666;
  padding: 10rpx 20rpx;
  border-radius: 8rpx;
  background-color: #f0f0f0;
}

.action-btn .icon {
  margin-right: 8rpx;
  font-size: 32rpx;
}

.select-mode-btn {
  color: #4a90e2;
  background-color: rgba(74, 144, 226, 0.1);
}

.exit-select-btn {
  color: #ff6b6b;
  background-color: rgba(255, 107, 107, 0.1);
}

.refresh-btn {
  color: #50e3c2;
  background-color: rgba(80, 227, 194, 0.1);
}

.create-btn {
  color: #4a90e2;
  background-color: rgba(74, 144, 226, 0.1);
}

/* 搜索栏样式 */
.search-bar {
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #eeeeee;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 10rpx 20rpx;
}

.search-icon {
  margin-right: 10rpx;
  color: #999999;
}

.search-input {
  flex: 1;
  height: 60rpx;
  font-size: 28rpx;
}

.clear-icon {
  color: #999999;
  padding: 10rpx;
}

/* 标签页样式 */
.tabs {
  display: flex;
  background-color: #ffffff;
  border-bottom: 1rpx solid #eeeeee;
}

.tab {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #666666;
  position: relative;
}

.tab.active {
  color: #4a90e2;
  font-weight: bold;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #4a90e2;
  border-radius: 2rpx;
}

/* 批量操作工具栏样式 */
.batch-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #f0f0f0;
  border-bottom: 1rpx solid #dddddd;
}

.selection-info {
  font-size: 28rpx;
  color: #666666;
}

.batch-actions {
  display: flex;
  gap: 20rpx;
}

.batch-btn {
  font-size: 28rpx;
  padding: 10rpx 20rpx;
  border-radius: 8rpx;
  text-align: center;
}

.select-all-btn {
  color: #4a90e2;
  background-color: rgba(74, 144, 226, 0.1);
}

.restore-btn {
  color: #50e3c2;
  background-color: rgba(80, 227, 194, 0.1);
}

.delete-btn {
  color: #ff6b6b;
  background-color: rgba(255, 107, 107, 0.1);
}

/* 加载状态样式 */
.loading-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #4a90e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999999;
}

/* 空状态样式 */
.empty-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #999999;
  margin-bottom: 20rpx;
}

.empty-action {
  font-size: 28rpx;
  color: #4a90e2;
  padding: 10rpx 30rpx;
  border-radius: 8rpx;
  background-color: rgba(74, 144, 226, 0.1);
}

/* 笔记列表样式 */
.notes-container {
  flex: 1;
  overflow-y: auto;
  padding: 20rpx;
}

.note-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.note-item.selectable {
  cursor: pointer;
}

.note-item.selected {
  background-color: rgba(74, 144, 226, 0.05);
  border: 1rpx solid #4a90e2;
}

.select-box {
  margin-right: 20rpx;
  padding-top: 6rpx;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #dddddd;
  border-radius: 8rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.checkbox.checked {
  background-color: #4a90e2;
  border-color: #4a90e2;
}

.check-icon {
  color: #ffffff;
  font-size: 28rpx;
}

.note-info {
  flex: 1;
}

.note-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
  word-break: break-all;
}

.note-content {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.note-meta {
  font-size: 24rpx;
  color: #999999;
}

.note-time {
  margin-right: 20rpx;
}

.note-actions {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  margin-left: 20rpx;
}

.note-actions .action-btn {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  white-space: nowrap;
}

.edit-btn {
  color: #4a90e2;
  background-color: rgba(74, 144, 226, 0.1);
}

/* 分页控制样式 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #eeeeee;
}

.page-btn {
  padding: 10rpx 20rpx;
  font-size: 28rpx;
  color: #4a90e2;
  background-color: rgba(74, 144, 226, 0.1);
  border-radius: 8rpx;
}

.page-btn.disabled {
  color: #cccccc;
  background-color: #f0f0f0;
  cursor: not-allowed;
}

.page-info {
  margin: 0 20rpx;
  font-size: 28rpx;
  color: #666666;
}

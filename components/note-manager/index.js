/**
 * 笔记管理组件
 * 用于管理用户的笔记，支持软删除功能
 */
Component({
  properties: {
    userId: {
      type: String,
      value: ''
    },
    showRecycleBin: {
      type: Boolean,
      value: false
    }
  },

  data: {
    notes: [],
    isLoading: false,
    isSelectMode: false,
    selectedNotes: [],
    pagination: {
      page: 1,
      pageSize: 10,
      total: 0
    },
    activeTab: 'all', // 'all', 'deleted'
    searchKeyword: '',

    // 新增功能数据
    // 筛选和排序
    filters: {
      category: '', // 分类筛选
      tag: '', // 标签筛选
      isPublic: '', // 公开状态筛选
      dateRange: '', // 时间范围筛选
      sortBy: 'updatedAt', // 排序字段: updatedAt, createdAt, title, viewCount
      sortOrder: 'desc' // 排序方向: asc, desc
    },

    // 可选的筛选选项
    categories: [], // 可选分类列表
    tags: [], // 可选标签列表

    // UI状态
    showFilterPanel: false, // 是否显示筛选面板
    showSortPanel: false, // 是否显示排序面板

    // 高级搜索
    advancedSearch: {
      title: '', // 标题搜索
      content: '', // 内容搜索
      author: '', // 作者搜索
      dateFrom: '', // 开始日期
      dateTo: '' // 结束日期
    },
    showAdvancedSearch: false, // 是否显示高级搜索

    // 视图模式
    viewMode: 'list', // 'list', 'grid', 'card'

    // 统计信息
    statistics: {
      totalNotes: 0,
      publicNotes: 0,
      privateNotes: 0,
      deletedNotes: 0,
      totalWords: 0
    }
  },

  lifetimes: {
    attached() {
      // 加载初始数据
      this.loadInitialData();
    }
  },

  methods: {
    /**
     * 加载初始数据
     */
    loadInitialData() {
      // 并行加载多个数据
      Promise.all([
        this.loadNotes(),
        this.loadCategories(),
        this.loadTags(),
        this.loadStatistics()
      ]).catch(err => {
        console.error('加载初始数据失败:', err);
      });
    },

    /**
     * 加载分类列表
     */
    loadCategories() {
      const api = getApp().globalData.api;
      if (!api) return Promise.reject('API客户端未初始化');

      return api.note.getCategories()
        .then(result => {
          this.setData({
            categories: result.data || []
          });
        })
        .catch(err => {
          console.error('加载分类失败:', err);
        });
    },

    /**
     * 加载标签列表
     */
    loadTags() {
      const api = getApp().globalData.api;
      if (!api) return Promise.reject('API客户端未初始化');

      return api.note.getTags()
        .then(result => {
          this.setData({
            tags: result.data || []
          });
        })
        .catch(err => {
          console.error('加载标签失败:', err);
        });
    },

    /**
     * 加载统计信息
     */
    loadStatistics() {
      const api = getApp().globalData.api;
      if (!api) return Promise.reject('API客户端未初始化');

      return api.note.getStatistics(this.properties.userId)
        .then(result => {
          this.setData({
            statistics: result.data || this.data.statistics
          });
        })
        .catch(err => {
          console.error('加载统计信息失败:', err);
        });
    },

    // 加载笔记列表
    loadNotes() {
      this.setData({ isLoading: true });

      const { page, pageSize } = this.data.pagination;
      const { filters, advancedSearch } = this.data;

      // 构建查询参数
      const params = {
        page,
        pageSize,
        userId: this.properties.userId,
        keyword: this.data.searchKeyword,

        // 筛选参数
        category: filters.category,
        tag: filters.tag,
        isPublic: filters.isPublic,
        dateRange: filters.dateRange,

        // 排序参数
        sortBy: filters.sortBy,
        sortOrder: filters.sortOrder,

        // 高级搜索参数
        titleKeyword: advancedSearch.title,
        contentKeyword: advancedSearch.content,
        author: advancedSearch.author,
        dateFrom: advancedSearch.dateFrom,
        dateTo: advancedSearch.dateTo
      };

      // 导入API客户端
      const api = getApp().globalData.api;
      if (!api) {
        console.error('[note-manager] API客户端未初始化');
        this.setData({ isLoading: false });
        wx.showToast({ title: '加载失败', icon: 'none' });
        return;
      }

      // 根据当前标签页加载不同数据
      const promise = this.data.activeTab === 'deleted'
        ? api.note.getDeletedNotes(params)
        : api.note.getNotes(params);

      promise.then(result => {
        this.setData({
          notes: result.data,
          'pagination.total': result.total || 0,
          isLoading: false
        });
      }).catch(err => {
        console.error('加载笔记列表失败:', err);
        wx.showToast({ title: '加载失败', icon: 'none' });
        this.setData({ isLoading: false });
      });
    },

    // 切换标签页
    switchTab(e) {
      const tab = e.currentTarget.dataset.tab;
      if (tab === this.data.activeTab) return;

      this.setData({
        activeTab: tab,
        'pagination.page': 1,
        selectedNotes: [],
        isSelectMode: false
      }, () => {
        this.loadNotes();
      });
    },

    // 搜索笔记
    handleSearch(e) {
      const keyword = e.detail.value;
      this.setData({
        searchKeyword: keyword,
        'pagination.page': 1
      }, () => {
        this.loadNotes();
      });
    },

    // 清除搜索
    clearSearch() {
      this.setData({
        searchKeyword: '',
        'pagination.page': 1
      }, () => {
        this.loadNotes();
      });
    },

    // 查看笔记详情
    viewNoteDetail(e) {
      const noteId = e.currentTarget.dataset.id;
      if (!noteId) return;

      // 触发查看详情事件
      this.triggerEvent('view', { id: noteId });
    },

    // 编辑笔记
    editNote(e) {
      const noteId = e.currentTarget.dataset.id;
      if (!noteId) return;

      // 触发编辑事件
      this.triggerEvent('edit', { id: noteId });
    },

    // 软删除笔记
    softDeleteNote(e) {
      const noteId = e.currentTarget.dataset.id;
      if (!noteId) return;

      wx.showModal({
        title: '删除确认',
        content: '确定要删除此笔记吗？删除后可在回收站恢复。',
        success: res => {
          if (res.confirm) {
            this.performSoftDelete(noteId);
          }
        }
      });
    },

    // 执行软删除
    performSoftDelete(noteId) {
      this.setData({ isLoading: true });

      // 导入API客户端
      const api = getApp().globalData.api;
      if (!api) {
        console.error('[note-manager] API客户端未初始化');
        this.setData({ isLoading: false });
        wx.showToast({ title: '删除失败', icon: 'none' });
        return;
      }

      api.note.softDeleteNote(noteId).then(() => {
        wx.showToast({ title: '删除成功', icon: 'success' });
        // 重新加载列表
        this.loadNotes();
        // 触发删除事件
        this.triggerEvent('delete', { id: noteId });
      }).catch(err => {
        console.error('删除笔记失败:', err);
        wx.showToast({ title: '删除失败', icon: 'none' });
        this.setData({ isLoading: false });
      });
    },

    // 恢复笔记
    restoreNote(e) {
      const noteId = e.currentTarget.dataset.id;
      if (!noteId) return;

      wx.showModal({
        title: '恢复确认',
        content: '确定要恢复此笔记吗？',
        success: res => {
          if (res.confirm) {
            this.performRestore(noteId);
          }
        }
      });
    },

    // 执行恢复
    performRestore(noteId) {
      this.setData({ isLoading: true });

      // 导入API客户端
      const api = getApp().globalData.api;
      if (!api) {
        console.error('[note-manager] API客户端未初始化');
        this.setData({ isLoading: false });
        wx.showToast({ title: '恢复失败', icon: 'none' });
        return;
      }

      api.note.restoreNote(noteId).then(() => {
        wx.showToast({ title: '恢复成功', icon: 'success' });
        // 重新加载列表
        this.loadNotes();
        // 触发恢复事件
        this.triggerEvent('restore', { id: noteId });
      }).catch(err => {
        console.error('恢复笔记失败:', err);
        wx.showToast({ title: '恢复失败', icon: 'none' });
        this.setData({ isLoading: false });
      });
    },

    // 切换选择模式
    toggleSelectMode() {
      this.setData({
        isSelectMode: !this.data.isSelectMode,
        selectedNotes: []
      });
    },

    // 选择/取消选择笔记
    toggleSelectNote(e) {
      if (!this.data.isSelectMode) return;

      const noteId = e.currentTarget.dataset.id;
      if (!noteId) return;

      const selectedNotes = [...this.data.selectedNotes];
      const index = selectedNotes.indexOf(noteId);

      if (index === -1) {
        selectedNotes.push(noteId);
      } else {
        selectedNotes.splice(index, 1);
      }

      this.setData({ selectedNotes });
    },

    // 全选/取消全选
    toggleSelectAll() {
      if (this.data.selectedNotes.length === this.data.notes.length) {
        // 取消全选
        this.setData({ selectedNotes: [] });
      } else {
        // 全选
        const selectedNotes = this.data.notes.map(note => note.id);
        this.setData({ selectedNotes });
      }
    },

    // 批量软删除
    batchSoftDelete() {
      const { selectedNotes } = this.data;
      if (selectedNotes.length === 0) {
        wx.showToast({ title: '请先选择笔记', icon: 'none' });
        return;
      }

      wx.showModal({
        title: '批量删除确认',
        content: `确定要删除选中的${selectedNotes.length}篇笔记吗？删除后可在回收站恢复。`,
        success: res => {
          if (res.confirm) {
            this.performBatchSoftDelete(selectedNotes);
          }
        }
      });
    },

    // 执行批量软删除
    performBatchSoftDelete(ids) {
      this.setData({ isLoading: true });

      // 导入API客户端
      const api = getApp().globalData.api;
      if (!api) {
        console.error('[note-manager] API客户端未初始化');
        this.setData({ isLoading: false });
        wx.showToast({ title: '批量删除失败', icon: 'none' });
        return;
      }

      api.note.batchSoftDeleteNotes(ids).then(result => {
        wx.showToast({
          title: `已删除${result.successCount || ids.length}篇`,
          icon: 'success'
        });

        // 重新加载列表
        this.loadNotes();

        // 退出选择模式
        this.setData({
          isSelectMode: false,
          selectedNotes: []
        });

        // 触发批量删除事件
        this.triggerEvent('batchDelete', { ids });
      }).catch(err => {
        console.error('批量删除失败:', err);
        wx.showToast({ title: '批量删除失败', icon: 'none' });
        this.setData({ isLoading: false });
      });
    },

    // 批量恢复
    batchRestore() {
      const { selectedNotes } = this.data;
      if (selectedNotes.length === 0) {
        wx.showToast({ title: '请先选择笔记', icon: 'none' });
        return;
      }

      wx.showModal({
        title: '批量恢复确认',
        content: `确定要恢复选中的${selectedNotes.length}篇笔记吗？`,
        success: res => {
          if (res.confirm) {
            this.performBatchRestore(selectedNotes);
          }
        }
      });
    },

    // 执行批量恢复
    performBatchRestore(ids) {
      this.setData({ isLoading: true });

      // 导入API客户端
      const api = getApp().globalData.api;
      if (!api) {
        console.error('[note-manager] API客户端未初始化');
        this.setData({ isLoading: false });
        wx.showToast({ title: '批量恢复失败', icon: 'none' });
        return;
      }

      api.note.batchRestoreNotes(ids).then(result => {
        wx.showToast({
          title: `已恢复${result.successCount || ids.length}篇`,
          icon: 'success'
        });

        // 重新加载列表
        this.loadNotes();

        // 退出选择模式
        this.setData({
          isSelectMode: false,
          selectedNotes: []
        });

        // 触发批量恢复事件
        this.triggerEvent('batchRestore', { ids });
      }).catch(err => {
        console.error('批量恢复失败:', err);
        wx.showToast({ title: '批量恢复失败', icon: 'none' });
        this.setData({ isLoading: false });
      });
    },

    // 分页处理 - 上一页
    handlePrevPage() {
      if (this.data.pagination.page <= 1) return;

      this.setData({
        'pagination.page': this.data.pagination.page - 1
      }, () => {
        this.loadNotes();
      });
    },

    // 分页处理 - 下一页
    handleNextPage() {
      const { page, pageSize, total } = this.data.pagination;
      if (page * pageSize >= total) return;

      this.setData({
        'pagination.page': page + 1
      }, () => {
        this.loadNotes();
      });
    },

    // 刷新列表
    refreshList() {
      this.setData({
        'pagination.page': 1,
        selectedNotes: [],
        isSelectMode: false
      }, () => {
        this.loadNotes();
      });
    },

    // 创建新笔记
    createNote() {
      // 触发创建事件
      this.triggerEvent('create');
    },

    /**
     * 显示/隐藏筛选面板
     */
    toggleFilterPanel() {
      this.setData({
        showFilterPanel: !this.data.showFilterPanel,
        showSortPanel: false,
        showAdvancedSearch: false
      });
    },

    /**
     * 显示/隐藏排序面板
     */
    toggleSortPanel() {
      this.setData({
        showSortPanel: !this.data.showSortPanel,
        showFilterPanel: false,
        showAdvancedSearch: false
      });
    },

    /**
     * 显示/隐藏高级搜索
     */
    toggleAdvancedSearch() {
      this.setData({
        showAdvancedSearch: !this.data.showAdvancedSearch,
        showFilterPanel: false,
        showSortPanel: false
      });
    },

    /**
     * 设置筛选条件
     */
    setFilter(e) {
      const { field, value } = e.currentTarget.dataset;
      const filters = { ...this.data.filters };
      filters[field] = value;

      this.setData({
        filters: filters,
        'pagination.page': 1
      }, () => {
        this.loadNotes();
      });
    },

    /**
     * 清除筛选条件
     */
    clearFilters() {
      this.setData({
        filters: {
          category: '',
          tag: '',
          isPublic: '',
          dateRange: '',
          sortBy: 'updatedAt',
          sortOrder: 'desc'
        },
        'pagination.page': 1
      }, () => {
        this.loadNotes();
      });
    },

    /**
     * 设置排序
     */
    setSort(e) {
      const { sortBy, sortOrder } = e.currentTarget.dataset;
      const filters = { ...this.data.filters };

      if (sortBy) filters.sortBy = sortBy;
      if (sortOrder) filters.sortOrder = sortOrder;

      this.setData({
        filters: filters,
        'pagination.page': 1,
        showSortPanel: false
      }, () => {
        this.loadNotes();
      });
    },

    /**
     * 高级搜索输入
     */
    handleAdvancedSearchInput(e) {
      const { field } = e.currentTarget.dataset;
      const value = e.detail.value;
      const advancedSearch = { ...this.data.advancedSearch };
      advancedSearch[field] = value;

      this.setData({
        advancedSearch: advancedSearch
      });
    },

    /**
     * 执行高级搜索
     */
    performAdvancedSearch() {
      this.setData({
        'pagination.page': 1,
        showAdvancedSearch: false
      }, () => {
        this.loadNotes();
      });
    },

    /**
     * 清除高级搜索
     */
    clearAdvancedSearch() {
      this.setData({
        advancedSearch: {
          title: '',
          content: '',
          author: '',
          dateFrom: '',
          dateTo: ''
        },
        'pagination.page': 1
      }, () => {
        this.loadNotes();
      });
    },

    /**
     * 切换视图模式
     */
    switchViewMode(e) {
      const mode = e.currentTarget.dataset.mode;
      this.setData({
        viewMode: mode
      });
    },

    /**
     * 导出笔记
     */
    exportNotes() {
      const { selectedNotes } = this.data;
      if (selectedNotes.length === 0) {
        wx.showToast({ title: '请先选择笔记', icon: 'none' });
        return;
      }

      wx.showModal({
        title: '导出确认',
        content: `确定要导出选中的${selectedNotes.length}篇笔记吗？`,
        success: res => {
          if (res.confirm) {
            this.performExport(selectedNotes);
          }
        }
      });
    },

    /**
     * 执行导出
     */
    performExport(ids) {
      wx.showLoading({ title: '导出中...' });

      const api = getApp().globalData.api;
      if (!api) {
        wx.hideLoading();
        wx.showToast({ title: '导出失败', icon: 'none' });
        return;
      }

      api.note.exportNotes(ids)
        .then(result => {
          wx.hideLoading();

          if (result.success && result.data.downloadUrl) {
            wx.showModal({
              title: '导出成功',
              content: '笔记已导出，是否立即下载？',
              success: res => {
                if (res.confirm) {
                  // 下载文件
                  wx.downloadFile({
                    url: result.data.downloadUrl,
                    success: downloadRes => {
                      wx.openDocument({
                        filePath: downloadRes.tempFilePath,
                        success: () => {
                          console.log('打开文档成功');
                        }
                      });
                    }
                  });
                }
              }
            });
          } else {
            wx.showToast({ title: '导出失败', icon: 'none' });
          }
        })
        .catch(err => {
          wx.hideLoading();
          console.error('导出失败:', err);
          wx.showToast({ title: '导出失败', icon: 'none' });
        });
    }
  }
});
